#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script simples para testar a nova funcionalidade
"""

import sys
import os

# Adicionar o diretório atual ao path para importar a classe
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from folha_pagamento import FolhaPagamento

def main():
    print("Testando a nova funcionalidade...")
    
    # Criar instância da classe
    folha = FolhaPagamento()
    
    # Verificar se há dados no banco
    funcionarios = folha.listar_funcionarios_banco()
    print(f"Funcionários no banco: {len(funcionarios)}")
    
    if funcionarios:
        print("Dados encontrados:")
        for func in funcionarios:
            print(f"  - {func[1]} ({func[2]}) - {func[3]}/{func[4]} - Salário <PERSON>íquido: R$ {func[25]:.2f}")
    else:
        print("Nenhum funcionário encontrado no banco.")
    
    print("Teste concluído com sucesso!")

if __name__ == "__main__":
    main()
