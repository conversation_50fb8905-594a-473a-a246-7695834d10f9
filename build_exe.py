#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para gerar o executável do Sistema de Folha de Pagamento
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def verificar_dependencias():
    """Verifica se todas as dependências estão instaladas"""
    print("🔍 Verificando dependências...")

    dependencias = [
        'tkinter',
        'pandas',
        'sqlite3'
    ]

    dependencias_opcionais = [
        'reportlab'
    ]

    for dep in dependencias:
        try:
            __import__(dep)
            print(f"  ✅ {dep}")
        except ImportError:
            print(f"  ❌ {dep} - NECESSÁRIO")
            return False

    # Verificar PyInstaller separadamente
    try:
        import pyinstaller
        print(f"  ✅ pyinstaller")
    except ImportError:
        print(f"  ⚠️  pyinstaller - será instalado automaticamente se necessário")

    for dep in dependencias_opcionais:
        try:
            __import__(dep)
            print(f"  ✅ {dep}")
        except ImportError:
            print(f"  ⚠️  {dep} - OPCIONAL (para exportar PDF)")

    return True

def instalar_pyinstaller():
    """Instala o PyInstaller se não estiver instalado"""
    try:
        import pyinstaller
        print("✅ PyInstaller já está instalado")
        return True
    except ImportError:
        print("📦 Instalando PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller instalado com sucesso")
            return True
        except subprocess.CalledProcessError:
            print("❌ Erro ao instalar PyInstaller")
            return False

def limpar_build():
    """Remove arquivos de build anteriores"""
    print("🧹 Limpando arquivos de build anteriores...")
    
    pastas_para_remover = ['build', 'dist', '__pycache__']
    arquivos_para_remover = ['*.pyc']
    
    for pasta in pastas_para_remover:
        if os.path.exists(pasta):
            shutil.rmtree(pasta)
            print(f"  🗑️  Removido: {pasta}")
    
    # Remover arquivos .pyc
    for arquivo in Path('.').rglob('*.pyc'):
        arquivo.unlink()
        print(f"  🗑️  Removido: {arquivo}")

def criar_executavel():
    """Cria o executável usando PyInstaller"""
    print("🔨 Criando executável...")
    
    comando = [
        'pyinstaller',
        '--onefile',                    # Arquivo único
        '--windowed',                   # Sem console
        '--name=FolhaPagamento',        # Nome do executável
        '--add-data=folha_pagamento.db;.',  # Incluir banco de dados
        '--hidden-import=tkinter',
        '--hidden-import=tkinter.ttk',
        '--hidden-import=tkinter.messagebox',
        '--hidden-import=tkinter.filedialog',
        '--hidden-import=pandas',
        '--hidden-import=sqlite3',
        '--clean',                      # Limpar cache
        'folha_pagamento.py'
    ]
    
    try:
        resultado = subprocess.run(comando, capture_output=True, text=True)
        
        if resultado.returncode == 0:
            print("✅ Executável criado com sucesso!")
            return True
        else:
            print("❌ Erro ao criar executável:")
            print(resultado.stderr)
            return False
            
    except FileNotFoundError:
        print("❌ PyInstaller não encontrado. Tentando instalar...")
        if instalar_pyinstaller():
            return criar_executavel()
        return False

def criar_pacote_distribuicao():
    """Cria um pacote completo para distribuição"""
    print("📦 Criando pacote de distribuição...")
    
    # Criar pasta de distribuição
    pasta_dist = Path('FolhaPagamento_Distribuicao')
    if pasta_dist.exists():
        shutil.rmtree(pasta_dist)
    
    pasta_dist.mkdir()
    
    # Copiar executável
    exe_origem = Path('dist/FolhaPagamento.exe')
    if exe_origem.exists():
        shutil.copy2(exe_origem, pasta_dist / 'FolhaPagamento.exe')
        print("  ✅ Executável copiado")
    else:
        print("  ❌ Executável não encontrado")
        return False
    
    # Copiar banco de dados (se existir)
    db_origem = Path('folha_pagamento.db')
    if db_origem.exists():
        shutil.copy2(db_origem, pasta_dist / 'folha_pagamento.db')
        print("  ✅ Banco de dados copiado")
    
    # Criar arquivo README
    readme_content = """
# Sistema de Folha de Pagamento

## Como usar:
1. Execute o arquivo FolhaPagamento.exe
2. O sistema criará automaticamente o banco de dados se não existir
3. Use as funcionalidades do menu principal

## Funcionalidades:
- Cadastrar funcionários
- Listar funcionários
- Gerar holerites
- Gerar folha de pagamento
- Exportar dados para CSV
- Exportar holerites para PDF (se reportlab estiver disponível)

## Requisitos:
- Windows 7 ou superior
- Não precisa instalar Python ou outras dependências

## Suporte:
- O banco de dados é criado automaticamente na primeira execução
- Todos os dados ficam salvos no arquivo folha_pagamento.db
- Para backup, copie o arquivo folha_pagamento.db

Desenvolvido com Python + Tkinter
"""
    
    with open(pasta_dist / 'README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("  ✅ README.txt criado")
    
    print(f"📦 Pacote criado em: {pasta_dist.absolute()}")
    return True

def main():
    """Função principal"""
    print("🚀 Iniciando build do Sistema de Folha de Pagamento")
    print("=" * 60)
    
    # Verificar dependências
    if not verificar_dependencias():
        print("❌ Dependências não atendidas. Instale as dependências necessárias.")
        return False
    
    # Limpar build anterior
    limpar_build()
    
    # Criar executável
    if not criar_executavel():
        print("❌ Falha ao criar executável")
        return False
    
    # Criar pacote de distribuição
    if not criar_pacote_distribuicao():
        print("❌ Falha ao criar pacote de distribuição")
        return False
    
    print("=" * 60)
    print("🎉 BUILD CONCLUÍDO COM SUCESSO!")
    print("📁 Pasta de distribuição: FolhaPagamento_Distribuicao")
    print("💾 Executável: FolhaPagamento.exe")
    print("📋 Instruções: README.txt")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    sucesso = main()
    if not sucesso:
        input("Pressione Enter para sair...")
        sys.exit(1)
    else:
        input("Pressione Enter para sair...")
