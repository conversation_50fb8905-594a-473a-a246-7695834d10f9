#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para testar os cálculos da folha de pagamento
"""

import sqlite3
import sys
import os

# Adicionar o diretório atual ao path para importar a classe
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from folha_pagamento import FolhaPagamento

def testar_calculos():
    """Testa os cálculos com os dados fornecidos"""
    
    # Dados de teste baseados no exemplo fornecido
    salario_base = 2200.0
    qtd_horas_extras = 5.0
    percentual_horas = 50.0
    
    # Criar instância da classe
    folha = FolhaPagamento()
    
    # Calcular valor das horas extras
    valor_hora = salario_base / 220  # 220 horas mensais
    horas_extras = valor_hora * (1 + percentual_horas/100) * qtd_horas_extras
    
    # Calcular salário bruto
    salario_bruto = salario_base + horas_extras
    
    # Calcular INSS
    inss = folha.calcular_inss(salario_bruto)
    
    # Calcular FGTS
    fgts = round(salario_bruto * 0.08, 2)
    
    # Calcular base IRRF
    base_irrf = salario_bruto - inss
    
    # Calcular IRRF
    irrf = folha.calcular_irrf(base_irrf)
    
    # Valores de descontos (exemplo)
    vale_transporte = 0.0
    vale_alimentacao = 0.0
    adiantamentos = 0.0
    outros_descontos = 0.0
    
    # Calcular outros descontos total
    outros_descontos_total = vale_transporte + vale_alimentacao + adiantamentos + outros_descontos
    
    # Calcular salário líquido
    salario_liquido = round(salario_bruto - inss - irrf - outros_descontos_total, 2)
    
    print("=== TESTE DE CÁLCULOS ===")
    print(f"Salário Base: R$ {salario_base:.2f}")
    print(f"Qtd Horas Extras: {qtd_horas_extras}")
    print(f"Percentual Horas: {percentual_horas}%")
    print(f"Valor Hora: R$ {valor_hora:.2f}")
    print(f"Horas Extras: R$ {horas_extras:.2f}")
    print(f"Salário Bruto: R$ {salario_bruto:.2f}")
    print(f"INSS: R$ {inss:.2f}")
    print(f"FGTS: R$ {fgts:.2f}")
    print(f"Base IRRF: R$ {base_irrf:.2f}")
    print(f"IRRF: R$ {irrf:.2f}")
    print(f"Vale Transporte: R$ {vale_transporte:.2f}")
    print(f"Vale Alimentação: R$ {vale_alimentacao:.2f}")
    print(f"Adiantamentos: R$ {adiantamentos:.2f}")
    print(f"Outros Descontos Total: R$ {outros_descontos_total:.2f}")
    print(f"Salário Líquido: R$ {salario_liquido:.2f}")
    
    print("\n=== COMPARAÇÃO COM DADOS FORNECIDOS ===")
    print("Dados fornecidos:")
    print("Salário Bruto: R$ 2275.0")
    print("INSS: R$ 183.57")
    print("FGTS: R$ 182.0")
    print("IRRF: R$ 0.0")
    print("Vale Transporte: R$ 2275.0 (ERRO)")
    print("Vale Alimentação: R$ 183.57 (ERRO)")
    print("Adiantamentos: R$ 182.0 (ERRO)")
    print("Descontos: R$ 2640.57 (ERRO)")
    print("Salário Líquido: R$ -549.14 (ERRO)")
    
    print("\n=== VERIFICAÇÃO ===")
    print(f"Salário Bruto calculado: R$ {salario_bruto:.2f} - {'✓' if abs(salario_bruto - 2275.0) < 0.01 else '✗'}")
    print(f"INSS calculado: R$ {inss:.2f} - {'✓' if abs(inss - 183.57) < 0.01 else '✗'}")
    print(f"FGTS calculado: R$ {fgts:.2f} - {'✓' if abs(fgts - 182.0) < 0.01 else '✗'}")
    print(f"IRRF calculado: R$ {irrf:.2f} - {'✓' if abs(irrf - 0.0) < 0.01 else '✗'}")
    
    # Testar inserção no banco
    print("\n=== TESTE DE INSERÇÃO NO BANCO ===")
    
    dados_teste = {
        'Nome': 'Sandro Denis da Silva Junior',
        'Cargo': 'Técnico em Informática',
        'Mês': 'Julho',
        'Ano': 2025,
        'Salário Base': salario_base,
        'Horas Extras': round(horas_extras, 2),
        'Qtd Horas Extras': qtd_horas_extras,
        'Percentual Horas': percentual_horas,
        'Salário Bruto': salario_bruto,
        'INSS': inss,
        'FGTS': fgts,
        'IRRF': irrf,
        'Vale Transporte': vale_transporte,
        'Vale Alimentação': vale_alimentacao,
        'Adiantamentos': adiantamentos,
        'Descontos': outros_descontos_total,
        '13º Proporcional': 0.0,
        'Férias Proporcionais': 0.0,
        'Outros Benefícios': 0.0,
        'Benefícios Descontados': False,
        'Adicional Noturno': 0.0,
        'Qtd Horas Noturnas': 0.0,
        'Insalubridade': 0.0,
        'Periculosidade': 0.0,
        'Salário Líquido': salario_liquido
    }
    
    if folha.inserir_funcionario_banco(dados_teste):
        print("✓ Dados inseridos com sucesso no banco!")
        
        # Verificar dados no banco
        funcionarios = folha.listar_funcionarios_banco()
        if funcionarios:
            func = funcionarios[0]
            print(f"Dados no banco:")
            print(f"  Nome: {func[1]}")
            print(f"  Salário Bruto: R$ {func[13]:.2f}")
            print(f"  INSS: R$ {func[14]:.2f}")
            print(f"  FGTS: R$ {func[15]:.2f}")
            print(f"  IRRF: R$ {func[16]:.2f}")
            print(f"  Vale Transporte: R$ {func[17]:.2f}")
            print(f"  Vale Alimentação: R$ {func[18]:.2f}")
            print(f"  Adiantamentos: R$ {func[19]:.2f}")
            print(f"  Descontos: R$ {func[20]:.2f}")
            print(f"  Salário Líquido: R$ {func[25]:.2f}")
    else:
        print("✗ Erro ao inserir dados no banco!")

if __name__ == "__main__":
    testar_calculos()
