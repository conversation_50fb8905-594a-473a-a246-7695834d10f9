#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para instalar todas as dependências necessárias
"""

import subprocess
import sys

def instalar_dependencias():
    """Instala todas as dependências necessárias"""
    
    dependencias = [
        'pandas',
        'pyinstaller',
        'reportlab'  # Opcional, mas recomendado para PDFs
    ]
    
    print("📦 Instalando dependências para o Sistema de Folha de Pagamento...")
    print("=" * 60)
    
    for dep in dependencias:
        print(f"📥 Instalando {dep}...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", dep, "--upgrade"
            ])
            print(f"✅ {dep} instalado com sucesso")
        except subprocess.CalledProcessError as e:
            print(f"❌ Erro ao instalar {dep}: {e}")
            if dep == 'reportlab':
                print("⚠️  reportlab é opcional - o sistema funcionará sem ele (sem exportação PDF)")
            else:
                print(f"❌ {dep} é necessário para o funcionamento do sistema")
                return False
        print("-" * 40)
    
    print("=" * 60)
    print("🎉 Todas as dependências foram instaladas!")
    print("🚀 Agora você pode executar: python build_exe.py")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    sucesso = instalar_dependencias()
    if not sucesso:
        input("❌ Falha na instalação. Pressione Enter para sair...")
        sys.exit(1)
    else:
        input("✅ Instalação concluída. Pressione Enter para sair...")
