#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para testar a exportação CSV
"""

import sys
import os

# Adicionar o diretório atual ao path para importar a classe
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from folha_pagamento import FolhaPagamento

def testar_csv():
    """Testa a exportação CSV"""
    
    # Criar instância da classe
    folha = FolhaPagamento()
    
    # Carregar dados para DataFrame
    folha.carregar_dados_para_dataframe()
    
    if not folha.df_funcionarios.empty:
        print("=== DADOS NO DATAFRAME ===")
        print(folha.df_funcionarios.to_string())
        
        # Exportar para CSV
        arquivo_csv = "teste_export.csv"
        folha.df_funcionarios.to_csv(arquivo_csv, index=False, encoding='utf-8-sig')
        print(f"\n=== CSV EXPORTADO PARA: {arquivo_csv} ===")
        
        # Ler e exibir o conteúdo do CSV
        with open(arquivo_csv, 'r', encoding='utf-8-sig') as f:
            conteudo = f.read()
            print("Conteúdo do CSV:")
            print(conteudo)
    else:
        print("Nenhum dado encontrado no DataFrame")

if __name__ == "__main__":
    testar_csv()
