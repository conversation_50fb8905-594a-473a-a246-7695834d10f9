# Folha de Pagamento Didática

Este é um projeto Python que implementa um sistema básico de folha de pagamento usando tkinter para a interface gráfica.

## Funcionalidades

- Interface gráfica com janela principal de 500x300 pixels
- Menu superior com três opções:
  - **Cadastrar Funcionário**: Para adicionar novos funcionários
  - **Listar Funcionários**: Para visualizar funcionários cadastrados
  - **G<PERSON><PERSON>**: Para gerar folhas de pagamento

## Requisitos

- Python 3.x
- pandas
- tkinter (incluído no Python)

## Como executar

1. Instale o pandas:
```bash
pip install pandas
```

2. Execute o programa:
```bash
python folha_pagamento.py
```

## Estrutura do Projeto

- `folha_pagamento.py`: Arquivo principal com a aplicação
- `README.md`: Documentação do projeto

## Status

Este é um projeto didático. As funcionalidades do menu estão preparadas para implementação futura.