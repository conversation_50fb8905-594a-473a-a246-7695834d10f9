#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para testar a nova interface de gerar holerite
"""

import sys
import os
import tkinter as tk

# Adicionar o diretório atual ao path para importar a classe
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from folha_pagamento import FolhaPagamento

def testar_nova_interface():
    """Testa a nova interface de gerar holerite"""
    
    # Criar instância da classe
    folha = FolhaPagamento()
    
    # Verificar se há dados no banco
    funcionarios = folha.listar_funcionarios_banco()
    print(f"Funcionários no banco: {len(funcionarios)}")
    
    if funcionarios:
        print("Dados encontrados:")
        for func in funcionarios:
            print(f"  - {func[1]} ({func[2]}) - {func[3]}/{func[4]}")
    
    # Criar janela principal para teste
    root = tk.Tk()
    root.title("Teste Nova Interface")
    root.geometry("300x200")
    
    # Definir a janela principal na instância
    folha.root = root
    
    # Botão para testar a nova interface
    tk.Button(root, text="Testar Nova Interface de Holerite", 
             command=folha.gerar_holerite,
             bg="#4CAF50", fg="white", font=("Arial", 12, "bold")).pack(pady=50)
    
    tk.Button(root, text="Fechar", command=root.destroy,
             bg="#f44336", fg="white", font=("Arial", 10, "bold")).pack(pady=10)
    
    print("Interface de teste iniciada. Clique no botão para testar a nova funcionalidade.")
    root.mainloop()

if __name__ == "__main__":
    testar_nova_interface()
