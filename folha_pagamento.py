import tkinter as tk
from tkinter import messagebox, ttk
import pandas as pd
import sqlite3
import os

# Verificação e importação da biblioteca reportlab para PDF
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.pdfgen import canvas
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    print("AVISO: Biblioteca reportlab não encontrada.")
    print("Para exportar PDFs, instale com: pip install reportlab")

class FolhaPagamento:
    def __init__(self):
        # Inicializar banco de dados
        self.db_path = 'folha_pagamento.db'
        self.inicializar_banco()
        
        # DataFrame apenas para relatórios (carregado quando necessário)
        self.df_funcionarios = pd.DataFrame()
        
        self.root = tk.Tk()
        self.root.title("Folha de Pagamento Didática")
        self.root.geometry("500x300")
        self.root.resizable(False, False)
        
        # Criar menu
        self.criar_menu()
        
        # Criar interface principal
        self.criar_interface_principal()
    
    def inicializar_banco(self):
        """Cria o banco de dados e a tabela funcionarios se não existirem"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Criar tabela funcionarios
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS funcionarios (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    nome TEXT NOT NULL,
                    cargo TEXT NOT NULL,
                    mes TEXT NOT NULL,
                    ano INTEGER NOT NULL,
                    salario_base REAL NOT NULL,
                    horas_extras REAL DEFAULT 0,
                    qtd_horas_extras REAL DEFAULT 0,
                    percentual_horas REAL DEFAULT 50,
                    salario_bruto REAL NOT NULL,
                    inss REAL NOT NULL,
                    fgts REAL NOT NULL,
                    irrf REAL NOT NULL,
                    vale_transporte REAL DEFAULT 0,
                    vale_alimentacao REAL DEFAULT 0,
                    adiantamentos REAL DEFAULT 0,
                    descontos REAL DEFAULT 0,
                    decimo_terceiro REAL DEFAULT 0,
                    ferias_proporcionais REAL DEFAULT 0,
                    outros_beneficios REAL DEFAULT 0,
                    beneficios_descontados BOOLEAN DEFAULT 0,
                    adicional_noturno REAL DEFAULT 0,
                    qtd_horas_noturnas REAL DEFAULT 0,
                    insalubridade REAL DEFAULT 0,
                    periculosidade REAL DEFAULT 0,
                    salario_liquido REAL NOT NULL,
                    data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Criar tabela cargos
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS cargos (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    nome TEXT NOT NULL UNIQUE,
                    salario_base REAL NOT NULL,
                    data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Adicionar novas colunas se a tabela já existir (migração)
            try:
                cursor.execute('ALTER TABLE funcionarios ADD COLUMN qtd_horas_extras REAL DEFAULT 0')
                cursor.execute('ALTER TABLE funcionarios ADD COLUMN percentual_horas REAL DEFAULT 50')
                cursor.execute('ALTER TABLE funcionarios ADD COLUMN vale_transporte REAL DEFAULT 0')
                cursor.execute('ALTER TABLE funcionarios ADD COLUMN vale_alimentacao REAL DEFAULT 0')
                cursor.execute('ALTER TABLE funcionarios ADD COLUMN adiantamentos REAL DEFAULT 0')
                cursor.execute('ALTER TABLE funcionarios ADD COLUMN outros_beneficios REAL DEFAULT 0')
                cursor.execute('ALTER TABLE funcionarios ADD COLUMN beneficios_descontados BOOLEAN DEFAULT 0')
                cursor.execute('ALTER TABLE funcionarios ADD COLUMN adicional_noturno REAL DEFAULT 0')
                cursor.execute('ALTER TABLE funcionarios ADD COLUMN qtd_horas_noturnas REAL DEFAULT 0')
                cursor.execute('ALTER TABLE funcionarios ADD COLUMN insalubridade REAL DEFAULT 0')
                cursor.execute('ALTER TABLE funcionarios ADD COLUMN periculosidade REAL DEFAULT 0')
            except sqlite3.OperationalError:
                # Colunas já existem
                pass
            
            conn.commit()
            conn.close()
            print(f"Banco de dados inicializado: {self.db_path}")
            
        except Exception as e:
            print(f"Erro ao inicializar banco de dados: {e}")
            messagebox.showerror("Erro", f"Erro ao inicializar banco de dados: {e}")
    
    def carregar_dados_para_dataframe(self):
        """Carrega dados do banco para DataFrame (usado apenas para relatórios)"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Carregar dados do banco
            query = '''
                SELECT nome as "Nome", cargo as "Cargo", mes as "Mês", ano as "Ano",
                       salario_base as "Salário Base", horas_extras as "Horas Extras",
                       adicional_noturno as "Adicional Noturno", insalubridade as "Insalubridade",
                       periculosidade as "Periculosidade", salario_bruto as "Salário Bruto", inss as "INSS", fgts as "FGTS",
                       irrf as "IRRF", vale_transporte as "Vale Transporte",
                       vale_alimentacao as "Vale Alimentação", adiantamentos as "Adiantamentos",
                       descontos as "Descontos", decimo_terceiro as "13º Proporcional",
                       ferias_proporcionais as "Férias Proporcionais",
                       outros_beneficios as "Outros Benefícios", beneficios_descontados as "Benefícios Descontados",
                       salario_liquido as "Salário Líquido"
                FROM funcionarios
                ORDER BY ano DESC, mes, nome
            '''
            
            self.df_funcionarios = pd.read_sql_query(query, conn)
            conn.close()
            
        except Exception as e:
            print(f"Erro ao carregar dados: {e}")
            self.df_funcionarios = pd.DataFrame()
    
    def inserir_funcionario_banco(self, dados_funcionario):
        """Insere funcionário no banco de dados"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO funcionarios (
                    nome, cargo, mes, ano, salario_base, horas_extras,
                    qtd_horas_extras, percentual_horas, adicional_noturno, qtd_horas_noturnas, insalubridade, periculosidade,
                    salario_bruto, inss, fgts, irrf,
                    vale_transporte, vale_alimentacao, adiantamentos, descontos,
                    decimo_terceiro, ferias_proporcionais, outros_beneficios, beneficios_descontados, salario_liquido
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                dados_funcionario['Nome'],
                dados_funcionario['Cargo'],
                dados_funcionario['Mês'],
                dados_funcionario['Ano'],
                dados_funcionario['Salário Base'],
                dados_funcionario['Horas Extras'],
                dados_funcionario.get('Qtd Horas Extras', 0),
                dados_funcionario.get('Percentual Horas', 50),
                dados_funcionario.get('Adicional Noturno', 0),
                dados_funcionario.get('Qtd Horas Noturnas', 0),
                dados_funcionario.get('Insalubridade', 0),
                dados_funcionario.get('Periculosidade', 0),
                dados_funcionario['Salário Bruto'],
                dados_funcionario['INSS'],
                dados_funcionario['FGTS'],
                dados_funcionario['IRRF'],
                dados_funcionario.get('Vale Transporte', 0),
                dados_funcionario.get('Vale Alimentação', 0),
                dados_funcionario.get('Adiantamentos', 0),
                dados_funcionario['Descontos'],
                dados_funcionario['13º Proporcional'],
                dados_funcionario['Férias Proporcionais'],
                dados_funcionario.get('Outros Benefícios', 0),
                dados_funcionario.get('Benefícios Descontados', 0),
                dados_funcionario['Salário Líquido']
            ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            print(f"Erro ao inserir funcionário: {e}")
            return False
    
    def listar_funcionarios_banco(self):
        """Lista todos os funcionários do banco"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, nome, cargo, mes, ano, salario_base, horas_extras,
                       qtd_horas_extras, percentual_horas, adicional_noturno, qtd_horas_noturnas, insalubridade, periculosidade,
                       salario_bruto, inss, fgts, irrf,
                       vale_transporte, vale_alimentacao, adiantamentos, descontos,
                       decimo_terceiro, ferias_proporcionais, outros_beneficios, beneficios_descontados, salario_liquido
                FROM funcionarios
                ORDER BY ano DESC, mes, nome
            ''')
            
            funcionarios = cursor.fetchall()
            conn.close()
            return funcionarios
            
        except Exception as e:
            print(f"Erro ao listar funcionários: {e}")
            return []
    
    def atualizar_funcionario_banco(self, funcionario_id, dados_funcionario):
        """Atualiza funcionário no banco de dados"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE funcionarios SET
                    nome = ?, cargo = ?, mes = ?, ano = ?, salario_base = ?,
                    horas_extras = ?, qtd_horas_extras = ?, percentual_horas = ?, adicional_noturno = ?, qtd_horas_noturnas = ?, insalubridade = ?, periculosidade = ?,
                    salario_bruto = ?, inss = ?, fgts = ?,
                    irrf = ?, vale_transporte = ?, vale_alimentacao = ?, adiantamentos = ?, descontos = ?, decimo_terceiro = ?,
                    ferias_proporcionais = ?, outros_beneficios = ?, beneficios_descontados = ?, salario_liquido = ?
                WHERE id = ?
            ''', (
                dados_funcionario['Nome'],
                dados_funcionario['Cargo'],
                dados_funcionario['Mês'],
                dados_funcionario['Ano'],
                dados_funcionario['Salário Base'],
                dados_funcionario['Horas Extras'],
                dados_funcionario.get('Qtd Horas Extras', 0),
                dados_funcionario.get('Percentual Horas', 50),
                dados_funcionario.get('Adicional Noturno', 0),
                dados_funcionario.get('Qtd Horas Noturnas', 0),
                dados_funcionario.get('Insalubridade', 0),
                dados_funcionario.get('Periculosidade', 0),
                dados_funcionario['Salário Bruto'],
                dados_funcionario['INSS'],
                dados_funcionario['FGTS'],
                dados_funcionario['IRRF'],
                dados_funcionario.get('Vale Transporte', 0),
                dados_funcionario.get('Vale Alimentação', 0),
                dados_funcionario.get('Adiantamentos', 0),
                dados_funcionario['Descontos'],
                dados_funcionario['13º Proporcional'],
                dados_funcionario['Férias Proporcionais'],
                dados_funcionario.get('Outros Benefícios', 0),
                dados_funcionario.get('Benefícios Descontados', 0),
                dados_funcionario['Salário Líquido'],
                funcionario_id
            ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            print(f"Erro ao atualizar funcionário: {e}")
            return False
    
    def excluir_funcionario_banco(self, funcionario_id):
        """Exclui funcionário do banco de dados"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('DELETE FROM funcionarios WHERE id = ?', (funcionario_id,))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            print(f"Erro ao excluir funcionário: {e}")
            return False
    
    def criar_interface_principal(self):
        """Cria a interface principal da aplicação"""
        # Label de boas-vindas
        label_titulo = tk.Label(self.root, text="Sistema de Folha de Pagamento", 
                               font=("Arial", 16, "bold"))
        label_titulo.pack(pady=20)
        
        label_instrucoes = tk.Label(self.root, text="Use o menu superior para navegar pelas opções", 
                                   font=("Arial", 10))
        label_instrucoes.pack(pady=10)
    
    def criar_menu(self):
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # Menu Cadastrar Funcionário
        menubar.add_command(label="Cadastrar Funcionário", command=self.cadastrar_funcionario)
        
        # Menu Gerenciar Cargos
        menubar.add_command(label="Gerenciar Cargos", command=self.gerenciar_cargos)
        
        # Menu Listar Funcionários
        menubar.add_command(label="Listar Funcionários", command=self.listar_funcionarios)
        
        # Menu Gerar Folha
        menubar.add_command(label="Gerar Folha", command=self.gerar_folha)
        
        # Menu Gerar Holerite
        menubar.add_command(label="Gerar Holerite", command=self.gerar_holerite)
    
    def calcular_inss(self, salario_bruto):
        """Calcula INSS conforme tabela progressiva 2024"""
        faixas = [
            (1412.00, 0.075),
            (2666.68, 0.09),
            (4000.03, 0.12),
            (7786.02, 0.14)
        ]
        
        inss = 0
        salario_restante = salario_bruto
        faixa_anterior = 0
        
        for limite, aliquota in faixas:
            if salario_restante <= 0:
                break
                
            faixa_atual = min(salario_restante, limite - faixa_anterior)
            inss += faixa_atual * aliquota
            salario_restante -= faixa_atual
            faixa_anterior = limite
            
            if salario_bruto <= limite:
                break
                
        return round(inss, 2)
    
    def calcular_irrf(self, base_irrf):
        """Calcula IRRF conforme tabela 2024"""
        if base_irrf <= 2259.20:
            return 0
        elif base_irrf <= 2826.65:
            return round(base_irrf * 0.075 - 169.44, 2)
        elif base_irrf <= 3751.05:
            return round(base_irrf * 0.15 - 381.44, 2)
        elif base_irrf <= 4664.68:
            return round(base_irrf * 0.225 - 662.77, 2)
        else:
            return round(base_irrf * 0.275 - 896.00, 2)
    
    def cadastrar_funcionario(self):
        # Criar janela de cadastro
        janela_cadastro = tk.Toplevel(self.root)
        janela_cadastro.title("Cadastrar Funcionário")
        janela_cadastro.geometry("450x600")
        janela_cadastro.resizable(True, True)
        
        # Criar frame principal com scrollbar
        canvas = tk.Canvas(janela_cadastro)
        scrollbar = ttk.Scrollbar(janela_cadastro, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Campos do formulário
        tk.Label(scrollable_frame, text="Nome:", font=("Arial", 10, "bold")).pack(pady=5)
        entry_nome = tk.Entry(scrollable_frame, width=40)
        entry_nome.pack(pady=5)
        
        tk.Label(scrollable_frame, text="Cargo:", font=("Arial", 10, "bold")).pack(pady=5)
        # Obter cargos cadastrados
        cargos_cadastrados = self.obter_cargos()
        nomes_cargos = [cargo[0] for cargo in cargos_cadastrados]
        
        combo_cargo = ttk.Combobox(scrollable_frame, values=nomes_cargos, width=37)
        combo_cargo.pack(pady=5)
        
        # Função para preencher salário automaticamente
        def on_cargo_selected(event):
            cargo_selecionado = combo_cargo.get()
            if cargo_selecionado:
                salario = self.obter_salario_cargo(cargo_selecionado)
                if salario:
                    entry_salario_base.delete(0, tk.END)
                    entry_salario_base.insert(0, str(salario))
        
        combo_cargo.bind('<<ComboboxSelected>>', on_cargo_selected)
        
        tk.Label(scrollable_frame, text="Mês:", font=("Arial", 10, "bold")).pack(pady=5)
        combo_mes = ttk.Combobox(scrollable_frame, values=[
            "Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho",
            "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"
        ], state="readonly", width=37)
        combo_mes.pack(pady=5)
        
        tk.Label(scrollable_frame, text="Ano:", font=("Arial", 10, "bold")).pack(pady=5)
        entry_ano = tk.Entry(scrollable_frame, width=40)
        entry_ano.pack(pady=5)
        
        tk.Label(scrollable_frame, text="Salário Base (R$):", font=("Arial", 10, "bold")).pack(pady=5)
        entry_salario_base = tk.Entry(scrollable_frame, width=40)
        entry_salario_base.pack(pady=5)
        
        tk.Label(scrollable_frame, text="Quantidade de Horas Extras:", font=("Arial", 10, "bold")).pack(pady=5)
        entry_qtd_horas_extras = tk.Entry(scrollable_frame, width=40)
        entry_qtd_horas_extras.pack(pady=5)
        
        tk.Label(scrollable_frame, text="Percentual de Horas Extras:", font=("Arial", 10, "bold")).pack(pady=5)
        combo_percentual_horas = ttk.Combobox(scrollable_frame, values=[
            "50%", "100%", "150%", "200%"
        ], state="readonly", width=37)
        combo_percentual_horas.pack(pady=5)
        combo_percentual_horas.set("50%")  # Valor padrão
        
        # Campos dos Adicionais Legais
        tk.Label(scrollable_frame, text="--- ADICIONAIS LEGAIS ---", font=("Arial", 10, "bold")).pack(pady=(15,5))
        
        tk.Label(scrollable_frame, text="Quantidade de Horas Noturnas (22h às 5h):", font=("Arial", 10, "bold")).pack(pady=5)
        entry_qtd_horas_noturnas = tk.Entry(scrollable_frame, width=40)
        entry_qtd_horas_noturnas.pack(pady=5)
        entry_qtd_horas_noturnas.insert(0, "0")
        
        tk.Label(scrollable_frame, text="Adicional Noturno (% do salário hora):", font=("Arial", 10, "bold")).pack(pady=5)
        combo_adicional_noturno = ttk.Combobox(scrollable_frame, values=[
            "0%", "20%", "25%", "30%"
        ], state="readonly", width=37)
        combo_adicional_noturno.pack(pady=5)
        combo_adicional_noturno.set("20%")  # Valor padrão conforme CLT
        
        tk.Label(scrollable_frame, text="Insalubridade (% do salário mínimo):", font=("Arial", 10, "bold")).pack(pady=5)
        combo_insalubridade = ttk.Combobox(scrollable_frame, values=[
            "0%", "10%", "20%", "40%"
        ], state="readonly", width=37)
        combo_insalubridade.pack(pady=5)
        combo_insalubridade.set("0%")  # Valor padrão
        
        tk.Label(scrollable_frame, text="Periculosidade (30% do salário base):", font=("Arial", 10, "bold")).pack(pady=5)
        var_periculosidade = tk.BooleanVar()
        checkbox_periculosidade = tk.Checkbutton(scrollable_frame, text="Aplicar Periculosidade (30% do salário base)", 
                                                variable=var_periculosidade, font=("Arial", 9))
        checkbox_periculosidade.pack(pady=5)
        
        # Novos campos de descontos detalhados
        tk.Label(scrollable_frame, text="Vale Transporte (VT) - R$:", font=("Arial", 10, "bold")).pack(pady=5)
        entry_vale_transporte = tk.Entry(scrollable_frame, width=40)
        entry_vale_transporte.pack(pady=5)
        
        tk.Label(scrollable_frame, text="Vale Alimentação/Refeição (VA) - R$:", font=("Arial", 10, "bold")).pack(pady=5)
        entry_vale_alimentacao = tk.Entry(scrollable_frame, width=40)
        entry_vale_alimentacao.pack(pady=5)
        
        tk.Label(scrollable_frame, text="Adiantamentos/Empréstimos - R$:", font=("Arial", 10, "bold")).pack(pady=5)
        entry_adiantamentos = tk.Entry(scrollable_frame, width=40)
        entry_adiantamentos.pack(pady=5)
        
        tk.Label(scrollable_frame, text="Outros Descontos (R$):", font=("Arial", 10, "bold")).pack(pady=5)
        entry_outros_descontos = tk.Entry(scrollable_frame, width=40)
        entry_outros_descontos.pack(pady=5)
        
        # Novo campo: Outros Benefícios
        tk.Label(scrollable_frame, text="Outros Benefícios (R$):", font=("Arial", 10, "bold")).pack(pady=5)
        entry_outros_beneficios = tk.Entry(scrollable_frame, width=40)
        entry_outros_beneficios.pack(pady=5)
        
        # Checkbox para descontar benefícios do funcionário
        var_descontar_beneficios = tk.BooleanVar()
        checkbox_descontar_beneficios = tk.Checkbutton(scrollable_frame, text="Descontar do funcionário (se desmarcado, será benefício pago pela empresa)", 
                                                      variable=var_descontar_beneficios, font=("Arial", 9))
        checkbox_descontar_beneficios.pack(pady=5)
        
        # Função para calcular VT automaticamente
        def calcular_vt():
            try:
                salario = float(entry_salario_base.get() or "0")
                vt_calculado = salario * 0.06  # 6% do salário base
                entry_vale_transporte.delete(0, tk.END)
                entry_vale_transporte.insert(0, f"{vt_calculado:.2f}")
            except ValueError:
                pass
        
        # Botão para calcular VT automaticamente
        tk.Button(scrollable_frame, text="Calcular VT (6% do Salário)", 
                 command=calcular_vt, bg="#2196F3", fg="white",
                 font=("Arial", 9)).pack(pady=5)
        
        # Checkbox para incluir 13º e férias
        var_incluir_13_ferias = tk.BooleanVar()
        checkbox_13_ferias = tk.Checkbutton(scrollable_frame, text="Incluir 13º Salário e Férias Proporcionais", 
                                          variable=var_incluir_13_ferias, font=("Arial", 10))
        checkbox_13_ferias.pack(pady=10)
        
        def salvar_funcionario():
            try:
                # Validar e obter dados
                nome = entry_nome.get().strip()
                cargo = combo_cargo.get().strip()
                mes = combo_mes.get()
                ano = int(entry_ano.get())
                salario_base = float(entry_salario_base.get())
                qtd_horas_extras = float(entry_qtd_horas_extras.get() or "0")
                percentual_str = combo_percentual_horas.get()
                percentual = float(percentual_str.replace("%", "")) if percentual_str else 50
                
                # Obter descontos detalhados
                vale_transporte = float(entry_vale_transporte.get() or "0")
                vale_alimentacao = float(entry_vale_alimentacao.get() or "0")
                adiantamentos = float(entry_adiantamentos.get() or "0")
                outros_descontos = float(entry_outros_descontos.get() or "0")
                
                # Obter dados dos benefícios
                outros_beneficios = float(entry_outros_beneficios.get() or "0")
                beneficios_descontados = var_descontar_beneficios.get()
                
                # Obter dados dos adicionais legais
                qtd_horas_noturnas = float(entry_qtd_horas_noturnas.get()) if entry_qtd_horas_noturnas.get() else 0
                adicional_noturno_str = combo_adicional_noturno.get()
                adicional_noturno_perc = float(adicional_noturno_str.replace("%", "")) if adicional_noturno_str != "0%" else 0
                
                insalubridade_str = combo_insalubridade.get()
                insalubridade_perc = float(insalubridade_str.replace("%", "")) if insalubridade_str != "0%" else 0
                
                periculosidade_aplicar = var_periculosidade.get()
                
                # Calcular outros descontos (não incluir INSS e IRRF que são calculados separadamente)
                outros_descontos_total = vale_transporte + vale_alimentacao + adiantamentos + outros_descontos
                if beneficios_descontados:
                    outros_descontos_total += outros_beneficios
                
                # Calcular valor das horas extras
                valor_hora = salario_base / 220  # 220 horas mensais
                horas_extras = valor_hora * (1 + percentual/100) * qtd_horas_extras
                
                # Calcular adicionais legais
                # Adicional Noturno: % do salário hora aplicado apenas às horas noturnas trabalhadas
                adicional_noturno_valor = (valor_hora * adicional_noturno_perc / 100) * qtd_horas_noturnas if adicional_noturno_perc > 0 and qtd_horas_noturnas > 0 else 0
                
                # Insalubridade: % do salário mínimo (R$ 1.320,00 em 2024)
                salario_minimo = 1320.00
                insalubridade_valor = salario_minimo * insalubridade_perc / 100 if insalubridade_perc > 0 else 0
                
                # Periculosidade: 30% do salário base
                periculosidade_valor = salario_base * 0.30 if periculosidade_aplicar else 0
                
                if not nome or not cargo or not mes:
                    messagebox.showerror("Erro", "Preencha todos os campos obrigatórios!")
                    return
                
                # Obter valor do checkbox
                incluir_13_ferias = var_incluir_13_ferias.get()
                
                # Cálculos
                salario_bruto = salario_base + horas_extras + adicional_noturno_valor + insalubridade_valor + periculosidade_valor
                inss = self.calcular_inss(salario_bruto)
                fgts = round(salario_bruto * 0.08, 2)
                base_irrf = salario_bruto - inss
                irrf = self.calcular_irrf(base_irrf)
                
                # Calcular 13º e férias se solicitado
                decimo_terceiro = 0
                ferias_proporcionais = 0
                if incluir_13_ferias:
                    # 13º proporcional (considerando 12 meses para simplificar)
                    decimo_terceiro = salario_base  # salario_base / 12 * 12
                    # Férias proporcionais com 1/3 constitucional
                    ferias_proporcionais = salario_base + (salario_base / 3)  # salario_base + 1/3
                
                # Calcular salário líquido (incluindo 13º e férias quando aplicável)
                salario_liquido = round(salario_bruto + decimo_terceiro + ferias_proporcionais - inss - irrf - outros_descontos_total, 2)
                
                # Criar dicionário com dados do funcionário
                novo_funcionario = {
                    'Nome': nome,
                    'Cargo': cargo,
                    'Mês': mes,
                    'Ano': ano,
                    'Salário Base': salario_base,
                    'Horas Extras': round(horas_extras, 2),
                    'Qtd Horas Extras': qtd_horas_extras,
                    'Percentual Horas': percentual,
                    'Salário Bruto': salario_bruto,
                    'INSS': inss,
                    'FGTS': fgts,
                    'IRRF': irrf,
                    'Vale Transporte': vale_transporte,
                    'Vale Alimentação': vale_alimentacao,
                    'Adiantamentos': adiantamentos,
                    'Descontos': outros_descontos_total,
                    '13º Proporcional': decimo_terceiro,
                    'Férias Proporcionais': ferias_proporcionais,
                    'Outros Benefícios': outros_beneficios,
                    'Benefícios Descontados': beneficios_descontados,
                    'Adicional Noturno': round(adicional_noturno_valor, 2),
                    'Qtd Horas Noturnas': qtd_horas_noturnas,
                    'Insalubridade': round(insalubridade_valor, 2),
                    'Periculosidade': round(periculosidade_valor, 2),
                    'Salário Líquido': salario_liquido
                }
                
                # Salvar no banco de dados
                if self.inserir_funcionario_banco(novo_funcionario):
                    # Mensagem de sucesso
                    messagebox.showinfo("Sucesso", 
                        f"Funcionário {nome} cadastrado com sucesso!\n\n"
                        f"Salário Bruto: R$ {salario_bruto:.2f}\n"
                        f"INSS: R$ {inss:.2f}\n"
                        f"IRRF: R$ {irrf:.2f}\n"
                        f"FGTS: R$ {fgts:.2f}\n"
                        f"Salário Líquido: R$ {salario_liquido:.2f}")
                    
                    janela_cadastro.destroy()
                else:
                    messagebox.showerror("Erro", "Erro ao salvar funcionário no banco de dados!")
                
            except ValueError:
                messagebox.showerror("Erro", "Verifique os valores numéricos inseridos!")
            except Exception as e:
                messagebox.showerror("Erro", f"Erro ao cadastrar funcionário: {str(e)}")
        
        # Botão salvar
        tk.Button(scrollable_frame, text="Salvar Funcionário", 
                 command=salvar_funcionario, bg="#4CAF50", fg="white",
                 font=("Arial", 10, "bold"), pady=5).pack(pady=20)
        
        # Configurar scroll com mouse wheel
        def _on_mousewheel(event):
            try:
                canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            except tk.TclError:
                # Canvas foi destruído, remover o bind
                canvas.unbind_all("<MouseWheel>")
        
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
        
        # Limpar bind quando a janela for fechada
        def on_closing():
            canvas.unbind_all("<MouseWheel>")
            janela_cadastro.destroy()
        
        janela_cadastro.protocol("WM_DELETE_WINDOW", on_closing)
    
    def listar_funcionarios(self):
        # Buscar funcionários do banco
        funcionarios = self.listar_funcionarios_banco()
        
        if not funcionarios:
            messagebox.showinfo("Lista Vazia", "Nenhum funcionário cadastrado ainda.")
            return
        
        # Criar janela de listagem
        janela_lista = tk.Toplevel(self.root)
        janela_lista.title("Lista de Funcionários")
        janela_lista.geometry("1200x700")
        janela_lista.resizable(True, True)
        janela_lista.minsize(800, 400)
        
        # Frame para a tabela com scrollbar
        frame_tabela = tk.Frame(janela_lista)
        frame_tabela.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Criar Treeview para exibir os dados
        colunas = ['Nome', 'Cargo', 'Mês', 'Ano', 'Salário Base', 'Horas Extras', 
                  'Adicional Noturno', 'Insalubridade', 'Periculosidade',
                  'Salário Bruto', 'INSS', 'FGTS', 'IRRF', 'Vale Transporte', 'Vale Alimentação', 'Adiantamentos', 'Descontos', '13º Proporcional', 'Férias Proporcionais', 'Outros Benefícios', 'Benefícios Descontados', 'Salário Líquido']
        
        tree = ttk.Treeview(frame_tabela, columns=colunas, show='headings', height=15)
        
        # Configurar cabeçalhos e larguras das colunas
        larguras = [120, 100, 80, 60, 90, 90, 90, 90, 90, 90, 70, 70, 70, 90, 90, 90, 80, 100, 120, 100, 80, 100]
        for i, col in enumerate(colunas):
            tree.heading(col, text=col)
            tree.column(col, width=larguras[i], minwidth=60)
        
        # Adicionar dados à tabela
        for funcionario in funcionarios:
            # Ordem dos campos na consulta SQL:
            # 0=id, 1=nome, 2=cargo, 3=mes, 4=ano, 5=salario_base, 6=horas_extras,
            # 7=qtd_horas_extras, 8=percentual_horas, 9=adicional_noturno, 10=qtd_horas_noturnas, 11=insalubridade, 12=periculosidade,
            # 13=salario_bruto, 14=inss, 15=fgts, 16=irrf,
            # 17=vale_transporte, 18=vale_alimentacao, 19=adiantamentos, 20=descontos,
            # 21=decimo_terceiro, 22=ferias_proporcionais, 23=outros_beneficios, 24=beneficios_descontados, 25=salario_liquido
            valores = [
                funcionario[1],  # Nome
                funcionario[2],  # Cargo
                funcionario[3],  # Mês
                str(funcionario[4]),  # Ano
                f"R$ {funcionario[5]:.2f}",  # Salário Base
                f"R$ {funcionario[6]:.2f}" if funcionario[6] > 0 else "R$ 0,00",  # Horas Extras
                f"R$ {funcionario[9]:.2f}" if funcionario[9] > 0 else "R$ 0,00",  # Adicional Noturno
                f"R$ {funcionario[11]:.2f}" if funcionario[11] > 0 else "R$ 0,00",  # Insalubridade
                f"R$ {funcionario[12]:.2f}" if funcionario[12] > 0 else "R$ 0,00",  # Periculosidade
                f"R$ {funcionario[13]:.2f}",  # Salário Bruto
                f"R$ {funcionario[14]:.2f}",  # INSS
                f"R$ {funcionario[15]:.2f}",  # FGTS
                f"R$ {funcionario[16]:.2f}",  # IRRF
                f"R$ {funcionario[17]:.2f}" if funcionario[17] > 0 else "R$ 0,00",  # Vale Transporte
                f"R$ {funcionario[18]:.2f}" if funcionario[18] > 0 else "R$ 0,00",  # Vale Alimentação
                f"R$ {funcionario[19]:.2f}" if funcionario[19] > 0 else "R$ 0,00",  # Adiantamentos
                f"R$ {funcionario[20]:.2f}" if funcionario[20] > 0 else "R$ 0,00",  # Descontos (Outros)
                f"R$ {funcionario[21]:.2f}" if funcionario[21] > 0 else "R$ 0,00",  # 13º Proporcional
                f"R$ {funcionario[22]:.2f}" if funcionario[22] > 0 else "R$ 0,00",  # Férias Proporcionais
                f"R$ {funcionario[23]:.2f}" if funcionario[23] > 0 else "R$ 0,00",  # Outros Benefícios
                "Sim" if funcionario[24] else "Não",  # Benefícios Descontados
                f"R$ {funcionario[25]:.2f}"   # Salário Líquido
            ]
            # Inserir com tag do ID do funcionário para referência
            tree.insert('', 'end', values=valores, tags=(str(funcionario[0]),))
        
        # Scrollbars
        scrollbar_v = ttk.Scrollbar(frame_tabela, orient=tk.VERTICAL, command=tree.yview)
        scrollbar_h = ttk.Scrollbar(frame_tabela, orient=tk.HORIZONTAL, command=tree.xview)
        tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        
        # Posicionar elementos
        tree.grid(row=0, column=0, sticky='nsew')
        scrollbar_v.grid(row=0, column=1, sticky='ns')
        scrollbar_h.grid(row=1, column=0, sticky='ew')
        
        # Configurar redimensionamento
        frame_tabela.grid_rowconfigure(0, weight=1)
        frame_tabela.grid_columnconfigure(0, weight=1)
        
        # Frame para botões
        frame_botoes = tk.Frame(janela_lista)
        frame_botoes.pack(fill=tk.X, padx=10, pady=5)
        
        def exportar_csv():
            try:
                from tkinter import filedialog
                arquivo = filedialog.asksaveasfilename(
                    defaultextension=".csv",
                    filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                    title="Salvar arquivo CSV"
                )
                if arquivo:
                    # Carregar dados do banco para DataFrame e exportar
                    self.carregar_dados_para_dataframe()
                    if not self.df_funcionarios.empty:
                        self.df_funcionarios.to_csv(arquivo, index=False, encoding='utf-8-sig')
                        messagebox.showinfo("Sucesso", f"Dados exportados para: {arquivo}")
                    else:
                        messagebox.showwarning("Aviso", "Nenhum dado para exportar!")
            except Exception as e:
                messagebox.showerror("Erro", f"Erro ao exportar CSV: {str(e)}")
        
        def editar_funcionario():
            selecionado = tree.selection()
            if not selecionado:
                messagebox.showwarning("Seleção", "Selecione um funcionário para editar!")
                return
            
            # Obter ID do funcionário selecionado
            item = tree.item(selecionado[0])
            funcionario_id = int(item['tags'][0])
            
            # Buscar dados do funcionário no banco
            funcionario_dados = None
            for func in funcionarios:
                if func[0] == funcionario_id:
                    funcionario_dados = func
                    break
            
            if not funcionario_dados:
                messagebox.showerror("Erro", "Funcionário não encontrado!")
                return
            
            # Criar janela de edição
            janela_edicao = tk.Toplevel(janela_lista)
            janela_edicao.title(f"Editar Funcionário - {funcionario_dados[1]}")
            janela_edicao.geometry("450x600")
            janela_edicao.resizable(True, True)
            
            # Criar frame principal com scrollbar para edição
            canvas_edit = tk.Canvas(janela_edicao)
            scrollbar_edit = ttk.Scrollbar(janela_edicao, orient="vertical", command=canvas_edit.yview)
            scrollable_frame_edit = ttk.Frame(canvas_edit)
            
            scrollable_frame_edit.bind(
                "<Configure>",
                lambda e: canvas_edit.configure(scrollregion=canvas_edit.bbox("all"))
            )
            
            canvas_edit.create_window((0, 0), window=scrollable_frame_edit, anchor="nw")
            canvas_edit.configure(yscrollcommand=scrollbar_edit.set)
            
            canvas_edit.pack(side="left", fill="both", expand=True)
            scrollbar_edit.pack(side="right", fill="y")
            
            # Campos do formulário preenchidos
            tk.Label(scrollable_frame_edit, text="Nome:", font=("Arial", 10, "bold")).pack(pady=5)
            entry_nome_edit = tk.Entry(scrollable_frame_edit, width=40)
            entry_nome_edit.pack(pady=5)
            entry_nome_edit.insert(0, funcionario_dados[1])  # Nome
            
            tk.Label(scrollable_frame_edit, text="Cargo:", font=("Arial", 10, "bold")).pack(pady=5)
            # Obter cargos cadastrados para edição
            cargos_cadastrados_edit = self.obter_cargos()
            nomes_cargos_edit = [cargo[0] for cargo in cargos_cadastrados_edit]
            
            combo_cargo_edit = ttk.Combobox(scrollable_frame_edit, values=nomes_cargos_edit, width=37)
            combo_cargo_edit.pack(pady=5)
            combo_cargo_edit.set(funcionario_dados[2])  # Cargo atual
            
            # Função para preencher salário automaticamente na edição
            def on_cargo_selected_edit(event):
                cargo_selecionado = combo_cargo_edit.get()
                if cargo_selecionado:
                    salario = self.obter_salario_cargo(cargo_selecionado)
                    if salario:
                        entry_salario_base_edit.delete(0, tk.END)
                        entry_salario_base_edit.insert(0, str(salario))
            
            combo_cargo_edit.bind('<<ComboboxSelected>>', on_cargo_selected_edit)
            
            tk.Label(scrollable_frame_edit, text="Mês:", font=("Arial", 10, "bold")).pack(pady=5)
            combo_mes_edit = ttk.Combobox(scrollable_frame_edit, values=[
                "Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho",
                "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"
            ], state="readonly", width=37)
            combo_mes_edit.pack(pady=5)
            combo_mes_edit.set(funcionario_dados[3])  # Mês
            
            tk.Label(scrollable_frame_edit, text="Ano:", font=("Arial", 10, "bold")).pack(pady=5)
            entry_ano_edit = tk.Entry(scrollable_frame_edit, width=40)
            entry_ano_edit.pack(pady=5)
            entry_ano_edit.insert(0, str(funcionario_dados[4]))  # Ano
            
            tk.Label(scrollable_frame_edit, text="Salário Base (R$):", font=("Arial", 10, "bold")).pack(pady=5)
            entry_salario_base_edit = tk.Entry(scrollable_frame_edit, width=40)
            entry_salario_base_edit.pack(pady=5)
            entry_salario_base_edit.insert(0, str(funcionario_dados[5]))  # Salário Base
            
            tk.Label(scrollable_frame_edit, text="Quantidade de Horas Extras:", font=("Arial", 10, "bold")).pack(pady=5)
            entry_qtd_horas_extras_edit = tk.Entry(scrollable_frame_edit, width=40)
            entry_qtd_horas_extras_edit.pack(pady=5)
            # Obter quantidade de horas extras do banco (posição 7)
            qtd_horas_atual = funcionario_dados[7] if funcionario_dados[7] else 0
            entry_qtd_horas_extras_edit.insert(0, str(qtd_horas_atual))
            
            tk.Label(scrollable_frame_edit, text="Percentual de Horas Extras:", font=("Arial", 10, "bold")).pack(pady=5)
            combo_percentual_horas_edit = ttk.Combobox(scrollable_frame_edit, values=[
                "50%", "100%", "150%", "200%"
            ], state="readonly", width=37)
            combo_percentual_horas_edit.pack(pady=5)
            # Obter percentual do banco (posição 8)
            percentual_atual = funcionario_dados[8] if funcionario_dados[8] else 50
            combo_percentual_horas_edit.set(f"{percentual_atual}%")
            
            # Novos campos de descontos detalhados na edição
            tk.Label(scrollable_frame_edit, text="Vale Transporte (VT) - R$:", font=("Arial", 10, "bold")).pack(pady=5)
            entry_vale_transporte_edit = tk.Entry(scrollable_frame_edit, width=40)
            entry_vale_transporte_edit.pack(pady=5)
            entry_vale_transporte_edit.insert(0, str(funcionario_dados[13] if funcionario_dados[13] else 0))  # Vale Transporte (posição 13)
            
            tk.Label(scrollable_frame_edit, text="Vale Alimentação/Refeição (VA) - R$:", font=("Arial", 10, "bold")).pack(pady=5)
            entry_vale_alimentacao_edit = tk.Entry(scrollable_frame_edit, width=40)
            entry_vale_alimentacao_edit.pack(pady=5)
            entry_vale_alimentacao_edit.insert(0, str(funcionario_dados[14] if funcionario_dados[14] else 0))  # Vale Alimentação (posição 14)
            
            tk.Label(scrollable_frame_edit, text="Adiantamentos/Empréstimos - R$:", font=("Arial", 10, "bold")).pack(pady=5)
            entry_adiantamentos_edit = tk.Entry(scrollable_frame_edit, width=40)
            entry_adiantamentos_edit.pack(pady=5)
            entry_adiantamentos_edit.insert(0, str(funcionario_dados[15] if funcionario_dados[15] else 0))  # Adiantamentos (posição 15)
            
            tk.Label(scrollable_frame_edit, text="Outros Descontos (R$):", font=("Arial", 10, "bold")).pack(pady=5)
            entry_outros_descontos_edit = tk.Entry(scrollable_frame_edit, width=40)
            entry_outros_descontos_edit.pack(pady=5)
            # Calcular outros descontos (total - VT - VA - Adiantamentos - Benefícios se descontados)
            total_descontos = funcionario_dados[16] if funcionario_dados[16] else 0  # Descontos totais (posição 16)
            vt_atual = funcionario_dados[13] if funcionario_dados[13] else 0
            va_atual = funcionario_dados[14] if funcionario_dados[14] else 0
            adiant_atual = funcionario_dados[15] if funcionario_dados[15] else 0
            beneficios_valor = funcionario_dados[19] if funcionario_dados[19] else 0  # Outros Benefícios (posição 19)
            beneficios_desc = funcionario_dados[20] if funcionario_dados[20] else 0  # Benefícios Descontados (posição 20)
            
            # Se benefícios são descontados, subtrair do total de descontos para calcular outros
            beneficios_descontados_valor = beneficios_valor if beneficios_desc else 0
            outros_atual = total_descontos - vt_atual - va_atual - adiant_atual - beneficios_descontados_valor
            entry_outros_descontos_edit.insert(0, str(max(0, outros_atual)))
            
            # Novos campos de benefícios na edição
            tk.Label(scrollable_frame_edit, text="Outros Benefícios (R$):", font=("Arial", 10, "bold")).pack(pady=5)
            entry_outros_beneficios_edit = tk.Entry(scrollable_frame_edit, width=40)
            entry_outros_beneficios_edit.pack(pady=5)
            entry_outros_beneficios_edit.insert(0, str(beneficios_valor))
            
            # Checkbox para descontar benefícios do funcionário na edição
            var_descontar_beneficios_edit = tk.BooleanVar()
            var_descontar_beneficios_edit.set(bool(beneficios_desc))
            checkbox_descontar_beneficios_edit = tk.Checkbutton(scrollable_frame_edit, text="Descontar do funcionário (se desmarcado, será benefício pago pela empresa)", 
                                                              variable=var_descontar_beneficios_edit, font=("Arial", 9))
            checkbox_descontar_beneficios_edit.pack(pady=5)
            
            # Função para calcular VT automaticamente na edição
            def calcular_vt_edit():
                try:
                    salario = float(entry_salario_base_edit.get() or "0")
                    vt_calculado = salario * 0.06  # 6% do salário base
                    entry_vale_transporte_edit.delete(0, tk.END)
                    entry_vale_transporte_edit.insert(0, f"{vt_calculado:.2f}")
                except ValueError:
                    pass
            
            # Botão para calcular VT automaticamente na edição
            tk.Button(scrollable_frame_edit, text="Calcular VT (6% do Salário)", 
                     command=calcular_vt_edit, bg="#2196F3", fg="white",
                     font=("Arial", 9)).pack(pady=5)
            
            # Campos dos Adicionais Legais na edição
            tk.Label(scrollable_frame_edit, text="--- ADICIONAIS LEGAIS ---", font=("Arial", 10, "bold")).pack(pady=(15,5))
            
            tk.Label(scrollable_frame_edit, text="Quantidade de Horas Noturnas (22h às 5h):", font=("Arial", 10, "bold")).pack(pady=5)
            entry_qtd_horas_noturnas_edit = tk.Entry(scrollable_frame_edit, width=40)
            entry_qtd_horas_noturnas_edit.pack(pady=5)
            # Obter quantidade de horas noturnas do banco (posição 23)
            qtd_horas_noturnas_atual = funcionario_dados[23] if len(funcionario_dados) > 23 and funcionario_dados[23] else 0
            entry_qtd_horas_noturnas_edit.insert(0, str(qtd_horas_noturnas_atual))
            
            tk.Label(scrollable_frame_edit, text="Adicional Noturno (% do salário hora):", font=("Arial", 10, "bold")).pack(pady=5)
            combo_adicional_noturno_edit = ttk.Combobox(scrollable_frame_edit, values=[
                "0%", "20%", "25%", "30%"
            ], state="readonly", width=37)
            combo_adicional_noturno_edit.pack(pady=5)
            # Calcular percentual atual baseado no valor salvo
            adicional_noturno_atual = funcionario_dados[22] if len(funcionario_dados) > 22 and funcionario_dados[22] else 0
            if adicional_noturno_atual > 0 and qtd_horas_noturnas_atual > 0:
                valor_hora_atual = funcionario_dados[5] / 220 if funcionario_dados[5] else 0
                percentual_atual = (adicional_noturno_atual / (valor_hora_atual * qtd_horas_noturnas_atual)) * 100 if valor_hora_atual > 0 else 0
                combo_adicional_noturno_edit.set(f"{percentual_atual:.0f}%")
            else:
                combo_adicional_noturno_edit.set("0%")
            
            tk.Label(scrollable_frame_edit, text="Insalubridade (% do salário mínimo):", font=("Arial", 10, "bold")).pack(pady=5)
            combo_insalubridade_edit = ttk.Combobox(scrollable_frame_edit, values=[
                "0%", "10%", "20%", "40%"
            ], state="readonly", width=37)
            combo_insalubridade_edit.pack(pady=5)
            # Calcular percentual atual de insalubridade
            insalubridade_atual = funcionario_dados[24] if len(funcionario_dados) > 24 and funcionario_dados[24] else 0
            if insalubridade_atual > 0:
                percentual_insalubridade = (insalubridade_atual / 1320.00) * 100
                combo_insalubridade_edit.set(f"{percentual_insalubridade:.0f}%")
            else:
                combo_insalubridade_edit.set("0%")
            
            tk.Label(scrollable_frame_edit, text="Periculosidade (30% do salário base):", font=("Arial", 10, "bold")).pack(pady=5)
            var_periculosidade_edit = tk.BooleanVar()
            periculosidade_atual = funcionario_dados[25] if len(funcionario_dados) > 25 and funcionario_dados[25] else 0
            var_periculosidade_edit.set(periculosidade_atual > 0)
            checkbox_periculosidade_edit = tk.Checkbutton(scrollable_frame_edit, text="Aplicar Periculosidade (30% do salário base)", 
                                                         variable=var_periculosidade_edit, font=("Arial", 9))
            checkbox_periculosidade_edit.pack(pady=5)
            
            # Checkbox para incluir 13º e férias na edição
            var_incluir_13_ferias_edit = tk.BooleanVar()
            # Verificar se já tem 13º ou férias cadastrados (posições 17 e 18)
            tem_13_ferias = (funcionario_dados[17] if funcionario_dados[17] else 0) > 0 or (funcionario_dados[18] if funcionario_dados[18] else 0) > 0  # 13º Proporcional ou Férias Proporcionais
            var_incluir_13_ferias_edit.set(tem_13_ferias)
            checkbox_13_ferias_edit = tk.Checkbutton(scrollable_frame_edit, text="Incluir 13º Salário e Férias Proporcionais", 
                                                    variable=var_incluir_13_ferias_edit, font=("Arial", 10))
            checkbox_13_ferias_edit.pack(pady=10)
            
            def salvar_edicao():
                try:
                    # Validar e obter dados
                    nome = entry_nome_edit.get().strip()
                    cargo = combo_cargo_edit.get().strip()
                    mes = combo_mes_edit.get()
                    ano = int(entry_ano_edit.get())
                    salario_base = float(entry_salario_base_edit.get())
                    qtd_horas_extras = float(entry_qtd_horas_extras_edit.get() or "0")
                    percentual_str = combo_percentual_horas_edit.get()
                    percentual = float(percentual_str.replace("%", "")) if percentual_str else 50
                    
                    # Obter descontos detalhados na edição
                    vale_transporte = float(entry_vale_transporte_edit.get() or "0")
                    vale_alimentacao = float(entry_vale_alimentacao_edit.get() or "0")
                    adiantamentos = float(entry_adiantamentos_edit.get() or "0")
                    outros_descontos = float(entry_outros_descontos_edit.get() or "0")
                    
                    # Obter dados dos benefícios na edição
                    outros_beneficios = float(entry_outros_beneficios_edit.get() or "0")
                    beneficios_descontados = var_descontar_beneficios_edit.get()
                    
                    # Obter dados dos adicionais legais na edição
                    qtd_horas_noturnas = float(entry_qtd_horas_noturnas_edit.get() or "0")
                    adicional_noturno_str = combo_adicional_noturno_edit.get()
                    adicional_noturno_perc = float(adicional_noturno_str.replace("%", "")) if adicional_noturno_str != "0%" else 0
                    
                    insalubridade_str = combo_insalubridade_edit.get()
                    insalubridade_perc = float(insalubridade_str.replace("%", "")) if insalubridade_str != "0%" else 0
                    
                    periculosidade_aplicar = var_periculosidade_edit.get()
                    
                    # Calcular outros descontos (não incluir INSS e IRRF que são calculados separadamente)
                    outros_descontos_total = vale_transporte + vale_alimentacao + adiantamentos + outros_descontos
                    if beneficios_descontados:
                        outros_descontos_total += outros_beneficios
                    
                    # Calcular valor das horas extras
                    valor_hora = salario_base / 220  # 220 horas mensais
                    horas_extras = valor_hora * (1 + percentual/100) * qtd_horas_extras
                    
                    # Calcular adicionais legais
                    # Adicional Noturno: % do salário hora aplicado apenas às horas noturnas trabalhadas
                    adicional_noturno_valor = (valor_hora * adicional_noturno_perc / 100) * qtd_horas_noturnas if adicional_noturno_perc > 0 and qtd_horas_noturnas > 0 else 0
                    
                    # Insalubridade: % do salário mínimo (R$ 1.320,00 em 2024)
                    salario_minimo = 1320.00
                    insalubridade_valor = salario_minimo * insalubridade_perc / 100 if insalubridade_perc > 0 else 0
                    
                    # Periculosidade: 30% do salário base
                    periculosidade_valor = salario_base * 0.30 if periculosidade_aplicar else 0
                    
                    if not nome or not cargo or not mes:
                        messagebox.showerror("Erro", "Preencha todos os campos obrigatórios!")
                        return
                    
                    # Obter valor do checkbox de edição
                    incluir_13_ferias_edit = var_incluir_13_ferias_edit.get()
                    
                    # Cálculos
                    salario_bruto = salario_base + horas_extras + adicional_noturno_valor + insalubridade_valor + periculosidade_valor
                    inss = self.calcular_inss(salario_bruto)
                    fgts = round(salario_bruto * 0.08, 2)
                    base_irrf = salario_bruto - inss
                    irrf = self.calcular_irrf(base_irrf)
                    
                    # Calcular 13º e férias se solicitado
                    decimo_terceiro = 0
                    ferias_proporcionais = 0
                    if incluir_13_ferias_edit:
                        # 13º proporcional (considerando 12 meses para simplificar)
                        decimo_terceiro = salario_base  # salario_base / 12 * 12
                        # Férias proporcionais com 1/3 constitucional
                        ferias_proporcionais = salario_base + (salario_base / 3)  # salario_base + 1/3
                    
                    # Calcular salário líquido (incluindo 13º e férias quando aplicável)
                    salario_liquido = round(salario_bruto + decimo_terceiro + ferias_proporcionais - inss - irrf - outros_descontos_total, 2)
                    
                    # Criar dicionário com dados atualizados
                    dados_atualizados = {
                        'Nome': nome,
                        'Cargo': cargo,
                        'Mês': mes,
                        'Ano': ano,
                        'Salário Base': salario_base,
                        'Horas Extras': round(horas_extras, 2),
                        'Qtd Horas Extras': qtd_horas_extras,
                        'Percentual Horas': percentual,
                        'Salário Bruto': salario_bruto,
                        'INSS': inss,
                        'FGTS': fgts,
                        'IRRF': irrf,
                        'Vale Transporte': vale_transporte,
                        'Vale Alimentação': vale_alimentacao,
                        'Adiantamentos': adiantamentos,
                        'Descontos': outros_descontos_total,
                        '13º Proporcional': decimo_terceiro,
                        'Férias Proporcionais': ferias_proporcionais,
                        'Salário Líquido': salario_liquido,
                        'Outros Benefícios': outros_beneficios,
                        'Benefícios Descontados': beneficios_descontados,
                        'Adicional Noturno': round(adicional_noturno_valor, 2),
                        'Qtd Horas Noturnas': qtd_horas_noturnas,
                        'Insalubridade': round(insalubridade_valor, 2),
                        'Periculosidade': round(periculosidade_valor, 2)
                    }
                    
                    # Atualizar no banco de dados
                    if self.atualizar_funcionario_banco(funcionario_id, dados_atualizados):
                        messagebox.showinfo("Sucesso", f"Funcionário {nome} editado com sucesso!")
                        janela_edicao.destroy()
                        janela_lista.destroy()
                        self.listar_funcionarios()  # Reabrir lista atualizada
                    else:
                        messagebox.showerror("Erro", "Erro ao atualizar funcionário no banco de dados!")
                    
                except ValueError:
                    messagebox.showerror("Erro", "Verifique os valores numéricos inseridos!")
                except Exception as e:
                    messagebox.showerror("Erro", f"Erro ao editar funcionário: {str(e)}")
            
            # Botões da edição
            frame_botoes_edit = tk.Frame(scrollable_frame_edit)
            frame_botoes_edit.pack(pady=20)
            
            tk.Button(frame_botoes_edit, text="Salvar Alterações", command=salvar_edicao,
                     bg="#4CAF50", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
            
            tk.Button(frame_botoes_edit, text="Cancelar", command=janela_edicao.destroy,
                     bg="#f44336", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
            
            # Configurar scroll com mouse wheel para edição
            def _on_mousewheel_edit(event):
                try:
                    canvas_edit.yview_scroll(int(-1*(event.delta/120)), "units")
                except tk.TclError:
                    # Canvas foi destruído, remover o bind
                    canvas_edit.unbind_all("<MouseWheel>")
            
            canvas_edit.bind_all("<MouseWheel>", _on_mousewheel_edit)
            
            # Limpar bind quando a janela de edição for fechada
            def on_closing_edit():
                canvas_edit.unbind_all("<MouseWheel>")
                janela_edicao.destroy()
            
            janela_edicao.protocol("WM_DELETE_WINDOW", on_closing_edit)
        
        def excluir_funcionario():
            selecionado = tree.selection()
            if not selecionado:
                messagebox.showwarning("Seleção", "Selecione um funcionário para excluir!")
                return
            
            # Obter ID do funcionário selecionado
            item = tree.item(selecionado[0])
            funcionario_id = int(item['tags'][0])
            
            # Buscar dados do funcionário no banco
            funcionario_dados = None
            for func in funcionarios:
                if func[0] == funcionario_id:
                    funcionario_dados = func
                    break
            
            if not funcionario_dados:
                messagebox.showerror("Erro", "Funcionário não encontrado!")
                return
            
            # Confirmar exclusão
            resposta = messagebox.askyesno(
                "Confirmar Exclusão",
                f"Tem certeza que deseja excluir o funcionário:\n\n"
                f"Nome: {funcionario_dados[1]}\n"
                f"Cargo: {funcionario_dados[2]}\n"
                f"Período: {funcionario_dados[3]}/{funcionario_dados[4]}\n\n"
                f"Esta ação não pode ser desfeita!"
            )
            
            if resposta:
                try:
                    # Remover do banco de dados
                    if self.excluir_funcionario_banco(funcionario_id):
                        messagebox.showinfo("Sucesso", f"Funcionário {funcionario_dados[1]} excluído com sucesso!")
                        janela_lista.destroy()
                        self.listar_funcionarios()  # Reabrir lista atualizada
                    else:
                        messagebox.showerror("Erro", "Erro ao excluir funcionário do banco de dados!")
                    
                except Exception as e:
                    messagebox.showerror("Erro", f"Erro ao excluir funcionário: {str(e)}")
        
        # Botões
        tk.Button(frame_botoes, text="Editar", command=editar_funcionario,
                 bg="#FF9800", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
        
        tk.Button(frame_botoes, text="Excluir", command=excluir_funcionario,
                 bg="#f44336", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
        
        tk.Button(frame_botoes, text="Exportar CSV", command=exportar_csv,
                 bg="#2196F3", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
        
        tk.Button(frame_botoes, text="Fechar", command=janela_lista.destroy,
                 bg="#9E9E9E", fg="white", font=("Arial", 10, "bold")).pack(side=tk.RIGHT, padx=5)
        
        # Label com total de funcionários
        total_funcionarios = len(funcionarios)
        tk.Label(frame_botoes, text=f"Total de funcionários: {total_funcionarios}",
                font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=20)
    
    def gerar_holerite(self):
        # Carregar dados do banco para DataFrame
        self.carregar_dados_para_dataframe()
        
        if self.df_funcionarios.empty:
            messagebox.showinfo("Lista Vazia", "Nenhum funcionário cadastrado ainda.")
            return
        
        # Criar janela de busca
        janela_busca = tk.Toplevel(self.root)
        janela_busca.title("Gerar Holerite")
        janela_busca.geometry("450x350")
        janela_busca.resizable(True, True)
        
        # Campos de busca
        tk.Label(janela_busca, text="Buscar Holerite", font=("Arial", 14, "bold")).pack(pady=10)
        
        tk.Label(janela_busca, text="Nome do Funcionário:", font=("Arial", 10, "bold")).pack(pady=5)
        entry_nome_busca = tk.Entry(janela_busca, width=40)
        entry_nome_busca.pack(pady=5)
        
        tk.Label(janela_busca, text="Mês:", font=("Arial", 10, "bold")).pack(pady=5)
        combo_mes_busca = ttk.Combobox(janela_busca, values=[
            "Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho",
            "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"
        ], state="readonly", width=37)
        combo_mes_busca.pack(pady=5)
        
        tk.Label(janela_busca, text="Ano:", font=("Arial", 10, "bold")).pack(pady=5)
        entry_ano_busca = tk.Entry(janela_busca, width=40)
        entry_ano_busca.pack(pady=5)
        
        def buscar_holerite():
            try:
                nome_busca = entry_nome_busca.get().strip()
                mes_busca = combo_mes_busca.get()
                ano_busca = int(entry_ano_busca.get())
                
                if not nome_busca or not mes_busca:
                    messagebox.showerror("Erro", "Preencha todos os campos!")
                    return
                
                # Buscar funcionário no DataFrame
                funcionario = self.df_funcionarios[
                    (self.df_funcionarios['Nome'].str.lower() == nome_busca.lower()) &
                    (self.df_funcionarios['Mês'] == mes_busca) &
                    (self.df_funcionarios['Ano'] == ano_busca)
                ]
                
                if funcionario.empty:
                    messagebox.showinfo("Não Encontrado", "Funcionário não encontrado para o mês/ano especificado.")
                    return
                
                # Obter dados do funcionário
                dados = funcionario.iloc[0]
                
                # Criar janela do holerite
                janela_holerite = tk.Toplevel(self.root)
                janela_holerite.title(f"Holerite - {dados['Nome']}")
                janela_holerite.geometry("600x700")
                janela_holerite.resizable(True, True)
                janela_holerite.minsize(500, 400)
                
                # Calcular outros descontos (total - VT - VA - Adiantamentos - Benefícios se descontados)
                vt = dados.get('Vale Transporte', 0)
                va = dados.get('Vale Alimentação', 0)
                adiantamentos = dados.get('Adiantamentos', 0)
                outros_beneficios = dados.get('Outros Benefícios', 0)
                beneficios_descontados = dados.get('Benefícios Descontados', False)
                
                # Criar texto do holerite
                info_adicionais = f"FGTS (8%)...........................R$ {dados['FGTS']:>10.2f}\n"
                
                # Adicionar 13º e férias se existirem
                if dados['13º Proporcional'] > 0:
                    info_adicionais += f"13º Salário Proporcional............R$ {dados['13º Proporcional']:>10.2f}\n"
                if dados['Férias Proporcionais'] > 0:
                    info_adicionais += f"Férias Proporcionais................R$ {dados['Férias Proporcionais']:>10.2f}\n"
                
                # Adicionar benefícios pagos pela empresa (não descontados)
                if outros_beneficios > 0 and not beneficios_descontados:
                    info_adicionais += f"Outros Benefícios (Empresa).........R$ {outros_beneficios:>10.2f}\n"
                
                # Se benefícios são descontados, subtrair do total de descontos
                beneficios_descontados_valor = outros_beneficios if beneficios_descontados else 0
                outros_descontos = dados['Descontos'] - vt - va - adiantamentos - beneficios_descontados_valor
                
                # Preparar texto dos adicionais legais
                adicionais_texto = ""
                if dados.get('Adicional Noturno', 0) > 0:
                    adicionais_texto += f"Adicional Noturno...................R$ {dados['Adicional Noturno']:>10.2f}\n"
                if dados.get('Insalubridade', 0) > 0:
                    adicionais_texto += f"Insalubridade.......................R$ {dados['Insalubridade']:>10.2f}\n"
                if dados.get('Periculosidade', 0) > 0:
                    adicionais_texto += f"Periculosidade......................R$ {dados['Periculosidade']:>10.2f}\n"
                
                texto_holerite = f"""
═══════════════════════════════════════════════════════
                    HOLERITE DE PAGAMENTO
═══════════════════════════════════════════════════════

FUNCIONÁRIO: {dados['Nome']}
CARGO: {dados['Cargo']}
PERÍODO: {dados['Mês']}/{dados['Ano']}

═══════════════════════════════════════════════════════
                      PROVENTOS
═══════════════════════════════════════════════════════

Salário Base........................R$ {dados['Salário Base']:>10.2f}
Horas Extras........................R$ {dados['Horas Extras']:>10.2f}
{adicionais_texto}                                   ─────────────────
SALÁRIO BRUTO.......................R$ {dados['Salário Bruto']:>10.2f}

═══════════════════════════════════════════════════════
                      DESCONTOS
═══════════════════════════════════════════════════════

INSS................................R$ {dados['INSS']:>10.2f}
IRRF................................R$ {dados['IRRF']:>10.2f}
Vale Transporte (VT)................R$ {vt:>10.2f}
Vale Alimentação/Refeição (VA).....R$ {va:>10.2f}
Adiantamentos/Empréstimos...........R$ {adiantamentos:>10.2f}
{('Outros Benefícios (Descontados)....R$ ' + f'{beneficios_descontados_valor:>10.2f}' + chr(10)) if beneficios_descontados_valor > 0 else ''}Outros Descontos....................R$ {outros_descontos:>10.2f}
                                   ─────────────────
TOTAL DE DESCONTOS..................R$ {(dados['INSS'] + dados['IRRF'] + dados['Descontos']):>10.2f}

═══════════════════════════════════════════════════════
                    INFORMAÇÕES ADICIONAIS
═══════════════════════════════════════════════════════

{info_adicionais}
═══════════════════════════════════════════════════════
                    VALOR LÍQUIDO A RECEBER
═══════════════════════════════════════════════════════

SALÁRIO LÍQUIDO.....................R$ {dados['Salário Líquido']:>10.2f}

═══════════════════════════════════════════════════════
"""
                
                # Widget de texto para exibir o holerite
                frame_texto = tk.Frame(janela_holerite)
                frame_texto.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
                
                text_widget = tk.Text(frame_texto, font=("Courier", 10), wrap=tk.WORD)
                scrollbar_texto = ttk.Scrollbar(frame_texto, orient=tk.VERTICAL, command=text_widget.yview)
                text_widget.configure(yscrollcommand=scrollbar_texto.set)
                
                text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
                scrollbar_texto.pack(side=tk.RIGHT, fill=tk.Y)
                
                text_widget.insert(tk.END, texto_holerite)
                text_widget.config(state=tk.DISABLED)  # Tornar somente leitura
                
                # Frame para botões do holerite
                frame_botoes_holerite = tk.Frame(janela_holerite)
                frame_botoes_holerite.pack(pady=10)
                
                def exportar_holerite_pdf():
                    if not REPORTLAB_AVAILABLE:
                        messagebox.showerror("Erro", "Biblioteca reportlab não instalada.\nInstale com: pip install reportlab")
                        return
                    
                    try:
                        from tkinter import filedialog
                        arquivo = filedialog.asksaveasfilename(
                            defaultextension=".pdf",
                            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                            title="Salvar Holerite PDF"
                        )
                        if arquivo:
                            # Criar documento PDF
                            doc = SimpleDocTemplate(arquivo, pagesize=A4)
                            styles = getSampleStyleSheet()
                            story = []
                            
                            # Título
                            title_style = ParagraphStyle(
                                'CustomTitle',
                                parent=styles['Heading1'],
                                fontSize=16,
                                spaceAfter=30,
                                alignment=1  # Centralizado
                            )
                            story.append(Paragraph("HOLERITE DE PAGAMENTO", title_style))
                            story.append(Spacer(1, 20))
                            
                            # Dados do funcionário
                            data = [
                                ['FUNCIONÁRIO:', dados['Nome']],
                                ['CARGO:', dados['Cargo']],
                                ['PERÍODO:', f"{dados['Mês']}/{dados['Ano']}"],
                                ['', ''],
                                ['PROVENTOS', ''],
                                ['Salário Base', f"R$ {dados['Salário Base']:.2f}"],
                                ['Horas Extras', f"R$ {dados['Horas Extras']:.2f}"]
                            ]
                            
                            # Adicionar adicionais legais se existirem
                            if dados.get('Adicional Noturno', 0) > 0:
                                data.append(['Adicional Noturno', f"R$ {dados['Adicional Noturno']:.2f}"])
                            if dados.get('Insalubridade', 0) > 0:
                                data.append(['Insalubridade', f"R$ {dados['Insalubridade']:.2f}"])
                            if dados.get('Periculosidade', 0) > 0:
                                data.append(['Periculosidade', f"R$ {dados['Periculosidade']:.2f}"])
                            
                            data.extend([
                                ['SALÁRIO BRUTO', f"R$ {dados['Salário Bruto']:.2f}"],
                                ['', ''],
                                ['DESCONTOS', ''],
                                ['INSS', f"R$ {dados['INSS']:.2f}"],
                                ['IRRF', f"R$ {dados['IRRF']:.2f}"],
                                ['Vale Transporte (VT)', f"R$ {vt:.2f}"],
                                ['Vale Alimentação/Refeição (VA)', f"R$ {va:.2f}"],
                                ['Adiantamentos/Empréstimos', f"R$ {adiantamentos:.2f}"]
                            ])
                            
                            # Adicionar benefícios descontados se existirem
                            if beneficios_descontados_valor > 0:
                                data.append(['Outros Benefícios (Descontados)', f"R$ {beneficios_descontados_valor:.2f}"])
                            
                            data.extend([
                                ['Outros Descontos', f"R$ {outros_descontos:.2f}"],
                                ['TOTAL DE DESCONTOS', f"R$ {(dados['INSS'] + dados['IRRF'] + dados['Descontos']):.2f}"],
                                ['', ''],
                                ['INFORMAÇÕES ADICIONAIS', ''],
                                ['FGTS (8%)', f"R$ {dados['FGTS']:.2f}"]
                            ])
                            
                            # Adicionar 13º e férias se existirem
                            if dados['13º Proporcional'] > 0:
                                data.append(['13º Salário Proporcional', f"R$ {dados['13º Proporcional']:.2f}"])
                            if dados['Férias Proporcionais'] > 0:
                                data.append(['Férias Proporcionais', f"R$ {dados['Férias Proporcionais']:.2f}"])
                            
                            # Adicionar benefícios pagos pela empresa (não descontados)
                            if outros_beneficios > 0 and not beneficios_descontados:
                                data.append(['Outros Benefícios (Empresa)', f"R$ {outros_beneficios:.2f}"])
                            
                            data.extend([
                                ['', ''],
                                ['SALÁRIO LÍQUIDO', f"R$ {dados['Salário Líquido']:.2f}"]
                            ])
                            
                            # Criar tabela
                            table = Table(data, colWidths=[3*inch, 2*inch])
                            table.setStyle(TableStyle([
                                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                                ('FONTSIZE', (0, 0), (-1, -1), 10),
                                ('FONTNAME', (0, 4), (0, 4), 'Helvetica-Bold'),  # PROVENTOS
                                ('FONTNAME', (0, 7), (0, 7), 'Helvetica-Bold'),  # SALÁRIO BRUTO
                                ('FONTNAME', (0, 9), (0, 9), 'Helvetica-Bold'),  # DESCONTOS
                                ('FONTNAME', (0, 13), (0, 13), 'Helvetica-Bold'), # TOTAL DESCONTOS
                                ('FONTNAME', (0, 15), (0, 15), 'Helvetica-Bold'), # INFO ADICIONAIS
                                ('FONTNAME', (0, 18), (0, 18), 'Helvetica-Bold'), # SALÁRIO LÍQUIDO
                                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                                ('BACKGROUND', (0, 4), (-1, 4), colors.lightgrey),
                                ('BACKGROUND', (0, 7), (-1, 7), colors.lightblue),
                                ('BACKGROUND', (0, 9), (-1, 9), colors.lightgrey),
                                ('BACKGROUND', (0, 13), (-1, 13), colors.lightyellow),
                                ('BACKGROUND', (0, 15), (-1, 15), colors.lightgrey),
                                ('BACKGROUND', (0, 18), (-1, 18), colors.lightgreen),
                            ]))
                            
                            story.append(table)
                            doc.build(story)
                            
                            messagebox.showinfo("Sucesso", f"Holerite exportado para: {arquivo}")
                    except Exception as e:
                        messagebox.showerror("Erro", f"Erro ao exportar PDF: {str(e)}")
                
                # Botões
                tk.Button(frame_botoes_holerite, text="Exportar PDF", command=exportar_holerite_pdf,
                         bg="#2196F3", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
                
                tk.Button(frame_botoes_holerite, text="Fechar", command=janela_holerite.destroy,
                         bg="#f44336", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
                
                janela_busca.destroy()
                
            except ValueError:
                messagebox.showerror("Erro", "Ano deve ser um número válido!")
            except Exception as e:
                messagebox.showerror("Erro", f"Erro ao gerar holerite: {str(e)}")
        
        # Botões
        frame_botoes = tk.Frame(janela_busca)
        frame_botoes.pack(pady=20)
        
        tk.Button(frame_botoes, text="Gerar Holerite", command=buscar_holerite,
                 bg="#4CAF50", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
        
        tk.Button(frame_botoes, text="Cancelar", command=janela_busca.destroy,
                 bg="#f44336", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
    
    def gerar_folha(self):
        # Carregar dados do banco para DataFrame
        self.carregar_dados_para_dataframe()
        
        if self.df_funcionarios.empty:
            messagebox.showinfo("Lista Vazia", "Nenhum funcionário cadastrado ainda.")
            return
        
        # Criar janela para selecionar período
        janela_periodo = tk.Toplevel(self.root)
        janela_periodo.title("Gerar Folha de Pagamento")
        janela_periodo.geometry("450x300")
        janela_periodo.resizable(True, True)
        
        # Campos para selecionar período
        tk.Label(janela_periodo, text="Selecionar Período", font=("Arial", 14, "bold")).pack(pady=10)
        
        tk.Label(janela_periodo, text="Mês:", font=("Arial", 10, "bold")).pack(pady=5)
        combo_mes_folha = ttk.Combobox(janela_periodo, values=[
            "Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho",
            "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"
        ], state="readonly", width=37)
        combo_mes_folha.pack(pady=5)
        
        tk.Label(janela_periodo, text="Ano:", font=("Arial", 10, "bold")).pack(pady=5)
        entry_ano_folha = tk.Entry(janela_periodo, width=40)
        entry_ano_folha.pack(pady=5)
        
        def calcular_folha():
            try:
                mes_folha = combo_mes_folha.get()
                ano_folha = int(entry_ano_folha.get())
                
                if not mes_folha:
                    messagebox.showerror("Erro", "Selecione o mês!")
                    return
                
                # Filtrar funcionários do período
                funcionarios_periodo = self.df_funcionarios[
                    (self.df_funcionarios['Mês'] == mes_folha) &
                    (self.df_funcionarios['Ano'] == ano_folha)
                ]
                
                if funcionarios_periodo.empty:
                    messagebox.showinfo("Sem Dados", f"Nenhum funcionário encontrado para {mes_folha}/{ano_folha}.")
                    return
                
                # Calcular totais
                quantidade_funcionarios = len(funcionarios_periodo)
                total_salarios_liquidos = funcionarios_periodo['Salário Líquido'].sum()
                total_salarios_brutos = funcionarios_periodo['Salário Bruto'].sum()
                total_inss = funcionarios_periodo['INSS'].sum()
                total_irrf = funcionarios_periodo['IRRF'].sum()
                total_fgts = funcionarios_periodo['FGTS'].sum()
                total_descontos = funcionarios_periodo['Descontos'].sum()
                total_vt = funcionarios_periodo['Vale Transporte'].sum()
                total_va = funcionarios_periodo['Vale Alimentação'].sum()
                total_adiantamentos = funcionarios_periodo['Adiantamentos'].sum()
                
                # Calcular totais de benefícios
                total_outros_beneficios = funcionarios_periodo['Outros Benefícios'].sum()
                # Benefícios descontados (onde Benefícios Descontados = True)
                beneficios_descontados_mask = funcionarios_periodo['Benefícios Descontados'] == True
                total_beneficios_descontados = funcionarios_periodo.loc[beneficios_descontados_mask, 'Outros Benefícios'].sum()
                # Benefícios pagos pela empresa (onde Benefícios Descontados = False)
                total_beneficios_empresa = total_outros_beneficios - total_beneficios_descontados
                
                total_outros_descontos = total_descontos - total_vt - total_va - total_adiantamentos - total_beneficios_descontados
                total_13_proporcional = funcionarios_periodo['13º Proporcional'].sum()
                total_ferias_proporcionais = funcionarios_periodo['Férias Proporcionais'].sum()
                
                # Calcular totais dos adicionais legais
                total_adicional_noturno = funcionarios_periodo['Adicional Noturno'].sum()
                total_insalubridade = funcionarios_periodo['Insalubridade'].sum()
                total_periculosidade = funcionarios_periodo['Periculosidade'].sum()
                
                # Criar janela de resultado
                janela_resultado = tk.Toplevel(self.root)
                janela_resultado.title(f"Folha de Pagamento - {mes_folha}/{ano_folha}")
                janela_resultado.geometry("700x600")
                janela_resultado.resizable(True, True)
                janela_resultado.minsize(600, 400)
                
                # Criar texto da folha
                texto_folha = f"""
═══════════════════════════════════════════════════════════════
                    FOLHA DE PAGAMENTO CONSOLIDADA
═══════════════════════════════════════════════════════════════

PERÍODO: {mes_folha}/{ano_folha}
DATA DE GERAÇÃO: {pd.Timestamp.now().strftime('%d/%m/%Y %H:%M')}

═══════════════════════════════════════════════════════════════
                         RESUMO GERAL
═══════════════════════════════════════════════════════════════

Quantidade de Funcionários..............{quantidade_funcionarios:>10}

═══════════════════════════════════════════════════════════════
                      TOTAIS FINANCEIROS
═══════════════════════════════════════════════════════════════

Total Salários Brutos...................R$ {total_salarios_brutos:>12.2f}

ADICIONAIS LEGAIS:
{('Total Adicional Noturno...............R$ ' + f'{total_adicional_noturno:>12.2f}' + chr(10)) if total_adicional_noturno > 0 else ''}{('Total Insalubridade.................R$ ' + f'{total_insalubridade:>12.2f}' + chr(10)) if total_insalubridade > 0 else ''}{('Total Periculosidade................R$ ' + f'{total_periculosidade:>12.2f}' + chr(10)) if total_periculosidade > 0 else ''}
DESCONTOS:
Total INSS..............................R$ {total_inss:>12.2f}
Total IRRF..............................R$ {total_irrf:>12.2f}
Total Vale Transporte (VT)..............R$ {total_vt:>12.2f}
Total Vale Alimentação/Refeição (VA)...R$ {total_va:>12.2f}
Total Adiantamentos/Empréstimos.........R$ {total_adiantamentos:>12.2f}
{('Total Outros Benefícios (Descontados).R$ ' + f'{total_beneficios_descontados:>12.2f}' + chr(10)) if total_beneficios_descontados > 0 else ''}Total Outros Descontos..................R$ {total_outros_descontos:>12.2f}
                                       ──────────────────
TOTAL DE DESCONTOS......................R$ {(total_inss + total_irrf + total_descontos):>12.2f}

INFORMAÇÕES ADICIONAIS:
Total FGTS (8%).........................R$ {total_fgts:>12.2f}
Total 13º Proporcional..................R$ {total_13_proporcional:>12.2f}
Total Férias Proporcionais..............R$ {total_ferias_proporcionais:>12.2f}
{('Total Outros Benefícios (Empresa).....R$ ' + f'{total_beneficios_empresa:>12.2f}' + chr(10)) if total_beneficios_empresa > 0 else ''}

═══════════════════════════════════════════════════════════════
                    VALOR LÍQUIDO TOTAL A PAGAR
═══════════════════════════════════════════════════════════════

TOTAL SALÁRIOS LÍQUIDOS.................R$ {total_salarios_liquidos:>12.2f}

═══════════════════════════════════════════════════════════════
                      DETALHAMENTO POR FUNCIONÁRIO
═══════════════════════════════════════════════════════════════
"""
                
                # Adicionar detalhes de cada funcionário
                for index, funcionario in funcionarios_periodo.iterrows():
                    texto_folha += f"""
{funcionario['Nome']:<30} | {funcionario['Cargo']:<20}
Salário Líquido: R$ {funcionario['Salário Líquido']:>10.2f}
{'-' * 65}
"""
                
                texto_folha += "\n═══════════════════════════════════════════════════════════════\n"
                
                # Widget de texto para exibir a folha
                frame_texto = tk.Frame(janela_resultado)
                frame_texto.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
                
                text_widget = tk.Text(frame_texto, font=("Courier", 9), wrap=tk.WORD)
                scrollbar_texto = ttk.Scrollbar(frame_texto, orient=tk.VERTICAL, command=text_widget.yview)
                text_widget.configure(yscrollcommand=scrollbar_texto.set)
                
                text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
                scrollbar_texto.pack(side=tk.RIGHT, fill=tk.Y)
                
                text_widget.insert(tk.END, texto_folha)
                text_widget.config(state=tk.DISABLED)  # Tornar somente leitura
                
                # Frame para botões
                frame_botoes_resultado = tk.Frame(janela_resultado)
                frame_botoes_resultado.pack(pady=10)
                
                def exportar_folha():
                    try:
                        from tkinter import filedialog
                        arquivo = filedialog.asksaveasfilename(
                            defaultextension=".txt",
                            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                            title="Salvar Folha de Pagamento"
                        )
                        if arquivo:
                            with open(arquivo, 'w', encoding='utf-8') as f:
                                f.write(texto_folha)
                            messagebox.showinfo("Sucesso", f"Folha exportada para: {arquivo}")
                    except Exception as e:
                        messagebox.showerror("Erro", f"Erro ao exportar folha: {str(e)}")
                
                def exportar_folha_pdf():
                    if not REPORTLAB_AVAILABLE:
                        messagebox.showerror("Erro", "Biblioteca reportlab não instalada.\nInstale com: pip install reportlab")
                        return
                    
                    try:
                        from tkinter import filedialog
                        arquivo = filedialog.asksaveasfilename(
                            defaultextension=".pdf",
                            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                            title="Salvar Folha de Pagamento PDF"
                        )
                        if arquivo:
                            # Criar documento PDF
                            doc = SimpleDocTemplate(arquivo, pagesize=A4)
                            styles = getSampleStyleSheet()
                            story = []
                            
                            # Título
                            title_style = ParagraphStyle(
                                'CustomTitle',
                                parent=styles['Heading1'],
                                fontSize=16,
                                spaceAfter=30,
                                alignment=1  # Centralizado
                            )
                            story.append(Paragraph("FOLHA DE PAGAMENTO CONSOLIDADA", title_style))
                            story.append(Spacer(1, 20))
                            
                            # Informações do período
                            info_style = ParagraphStyle(
                                'InfoStyle',
                                parent=styles['Normal'],
                                fontSize=12,
                                spaceAfter=10
                            )
                            story.append(Paragraph(f"<b>PERÍODO:</b> {mes_folha}/{ano_folha}", info_style))
                            story.append(Paragraph(f"<b>DATA DE GERAÇÃO:</b> {pd.Timestamp.now().strftime('%d/%m/%Y %H:%M')}", info_style))
                            story.append(Spacer(1, 20))
                            
                            # Resumo geral
                            data_resumo = [
                                ['RESUMO GERAL', ''],
                                ['Quantidade de Funcionários', str(quantidade_funcionarios)],
                                ['', ''],
                                ['TOTAIS FINANCEIROS', ''],
                                ['Total Salários Brutos', f"R$ {total_salarios_brutos:.2f}"],
                                ['', ''],
                                ['ADICIONAIS LEGAIS', ''],
                            ]
                            
                            # Adicionar adicionais legais se houver valores
                            if total_adicional_noturno > 0:
                                data_resumo.append(['Total Adicional Noturno', f"R$ {total_adicional_noturno:.2f}"])
                            if total_insalubridade > 0:
                                data_resumo.append(['Total Insalubridade', f"R$ {total_insalubridade:.2f}"])
                            if total_periculosidade > 0:
                                data_resumo.append(['Total Periculosidade', f"R$ {total_periculosidade:.2f}"])
                            
                            data_resumo.extend([
                                ['', ''],
                                ['DESCONTOS', ''],
                                ['Total INSS', f"R$ {total_inss:.2f}"],
                                ['Total IRRF', f"R$ {total_irrf:.2f}"],
                                ['Total Vale Transporte (VT)', f"R$ {total_vt:.2f}"],
                                ['Total Vale Alimentação/Refeição (VA)', f"R$ {total_va:.2f}"],
                                ['Total Adiantamentos/Empréstimos', f"R$ {total_adiantamentos:.2f}"],
                                ['Total Outros Descontos', f"R$ {total_outros_descontos:.2f}"],
                                ['TOTAL DE DESCONTOS', f"R$ {(total_inss + total_irrf + total_descontos):.2f}"],
                                ['', ''],
                                ['INFORMAÇÕES ADICIONAIS', ''],
                                ['Total FGTS (8%)', f"R$ {total_fgts:.2f}"],
                                ['Total 13º Proporcional', f"R$ {total_13_proporcional:.2f}"],
                                ['Total Férias Proporcionais', f"R$ {total_ferias_proporcionais:.2f}"],
                                ['', ''],
                                ['VALOR LÍQUIDO TOTAL A PAGAR', ''],
                                ['TOTAL SALÁRIOS LÍQUIDOS', f"R$ {total_salarios_liquidos:.2f}"]
                            ])
                            
                            # Criar tabela de resumo
                            table_resumo = Table(data_resumo, colWidths=[3.5*inch, 2*inch])
                            # Aplicar estilos dinamicamente baseado no conteúdo
                            table_style = [
                                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                                ('FONTSIZE', (0, 0), (-1, -1), 10),
                                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                            ]
                            
                            # Aplicar estilos para cada seção
                            for i, row in enumerate(data_resumo):
                                if row[0] in ['RESUMO GERAL', 'TOTAIS FINANCEIROS', 'ADICIONAIS LEGAIS', 'DESCONTOS', 'INFORMAÇÕES ADICIONAIS', 'VALOR LÍQUIDO TOTAL A PAGAR']:
                                    table_style.append(('FONTNAME', (0, i), (0, i), 'Helvetica-Bold'))
                                    table_style.append(('BACKGROUND', (0, i), (-1, i), colors.lightgrey))
                                elif row[0] == 'TOTAL DE DESCONTOS':
                                    table_style.append(('FONTNAME', (0, i), (0, i), 'Helvetica-Bold'))
                                    table_style.append(('BACKGROUND', (0, i), (-1, i), colors.lightyellow))
                                elif row[0] == 'TOTAL SALÁRIOS LÍQUIDOS':
                                    table_style.append(('FONTNAME', (0, i), (0, i), 'Helvetica-Bold'))
                                    table_style.append(('BACKGROUND', (0, i), (-1, i), colors.lightgreen))
                            
                            table_resumo.setStyle(TableStyle(table_style))
                            
                            story.append(table_resumo)
                            story.append(Spacer(1, 30))
                            
                            # Detalhamento por funcionário
                            story.append(Paragraph("<b>DETALHAMENTO POR FUNCIONÁRIO</b>", info_style))
                            story.append(Spacer(1, 10))
                            
                            # Tabela de funcionários
                            data_funcionarios = [['Nome', 'Cargo', 'Salário Líquido']]
                            for index, funcionario in funcionarios_periodo.iterrows():
                                data_funcionarios.append([
                                    funcionario['Nome'],
                                    funcionario['Cargo'],
                                    f"R$ {funcionario['Salário Líquido']:.2f}"
                                ])
                            
                            table_funcionarios = Table(data_funcionarios, colWidths=[2*inch, 2*inch, 1.5*inch])
                            table_funcionarios.setStyle(TableStyle([
                                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                                ('FONTSIZE', (0, 0), (-1, -1), 9),
                                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                                ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
                            ]))
                            
                            story.append(table_funcionarios)
                            doc.build(story)
                            
                            messagebox.showinfo("Sucesso", f"Folha de pagamento exportada para: {arquivo}")
                    except Exception as e:
                        messagebox.showerror("Erro", f"Erro ao exportar PDF: {str(e)}")
                
                # Botões
                tk.Button(frame_botoes_resultado, text="Exportar Folha", command=exportar_folha,
                         bg="#4CAF50", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
                
                tk.Button(frame_botoes_resultado, text="Exportar PDF", command=exportar_folha_pdf,
                         bg="#2196F3", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
                
                tk.Button(frame_botoes_resultado, text="Fechar", command=janela_resultado.destroy,
                         bg="#f44336", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
                
                janela_periodo.destroy()
                
            except ValueError:
                messagebox.showerror("Erro", "Ano deve ser um número válido!")
            except Exception as e:
                messagebox.showerror("Erro", f"Erro ao gerar folha: {str(e)}")
        
        # Botões da janela de período
        frame_botoes = tk.Frame(janela_periodo)
        frame_botoes.pack(pady=20)
        
        tk.Button(frame_botoes, text="Gerar Folha", command=calcular_folha,
                 bg="#4CAF50", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
        
        tk.Button(frame_botoes, text="Cancelar", command=janela_periodo.destroy,
                 bg="#f44336", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
    
    def gerenciar_cargos(self):
        """Abre janela para gerenciar cargos padrão"""
        janela_cargos = tk.Toplevel(self.root)
        janela_cargos.title("Gerenciar Cargos")
        janela_cargos.geometry("500x400")
        janela_cargos.resizable(False, False)
        
        # Frame principal
        frame_principal = tk.Frame(janela_cargos)
        frame_principal.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Campos de entrada
        tk.Label(frame_principal, text="Nome do Cargo:", font=("Arial", 10, "bold")).pack(pady=5)
        entry_nome_cargo = tk.Entry(frame_principal, width=40)
        entry_nome_cargo.pack(pady=5)
        
        tk.Label(frame_principal, text="Salário Base (R$):", font=("Arial", 10, "bold")).pack(pady=5)
        entry_salario_cargo = tk.Entry(frame_principal, width=40)
        entry_salario_cargo.pack(pady=5)
        
        # Frame para botões
        frame_botoes = tk.Frame(frame_principal)
        frame_botoes.pack(pady=10)
        
        # Lista de cargos
        tk.Label(frame_principal, text="Cargos Cadastrados:", font=("Arial", 10, "bold")).pack(pady=(20,5))
        
        # Treeview para mostrar cargos
        colunas = ("ID", "Nome", "Salário Base")
        tree_cargos = ttk.Treeview(frame_principal, columns=colunas, show="headings", height=8)
        
        for col in colunas:
            tree_cargos.heading(col, text=col)
            tree_cargos.column(col, width=150)
        
        tree_cargos.pack(pady=5, fill="both", expand=True)
        
        # Scrollbar para a lista
        scrollbar_cargos = ttk.Scrollbar(frame_principal, orient="vertical", command=tree_cargos.yview)
        tree_cargos.configure(yscrollcommand=scrollbar_cargos.set)
        scrollbar_cargos.pack(side="right", fill="y")
        
        def carregar_cargos():
            """Carrega cargos do banco de dados na lista"""
            for item in tree_cargos.get_children():
                tree_cargos.delete(item)
            
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT id, nome, salario_base FROM cargos ORDER BY nome")
                cargos = cursor.fetchall()
                conn.close()
                
                for cargo in cargos:
                    tree_cargos.insert("", "end", values=(cargo[0], cargo[1], f"R$ {cargo[2]:.2f}"))
            except Exception as e:
                messagebox.showerror("Erro", f"Erro ao carregar cargos: {e}")
        
        def adicionar_cargo():
            """Adiciona novo cargo ao banco de dados"""
            nome = entry_nome_cargo.get().strip()
            salario_str = entry_salario_cargo.get().strip()
            
            if not nome or not salario_str:
                messagebox.showerror("Erro", "Preencha todos os campos!")
                return
            
            try:
                salario = float(salario_str.replace(",", "."))
                if salario <= 0:
                    messagebox.showerror("Erro", "Salário deve ser maior que zero!")
                    return
                
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute("INSERT INTO cargos (nome, salario_base) VALUES (?, ?)", (nome, salario))
                conn.commit()
                conn.close()
                
                messagebox.showinfo("Sucesso", "Cargo adicionado com sucesso!")
                entry_nome_cargo.delete(0, tk.END)
                entry_salario_cargo.delete(0, tk.END)
                carregar_cargos()
                
            except ValueError:
                messagebox.showerror("Erro", "Salário deve ser um número válido!")
            except sqlite3.IntegrityError:
                messagebox.showerror("Erro", "Já existe um cargo com este nome!")
            except Exception as e:
                messagebox.showerror("Erro", f"Erro ao adicionar cargo: {e}")
        
        def excluir_cargo():
            """Exclui cargo selecionado"""
            selecionado = tree_cargos.selection()
            if not selecionado:
                messagebox.showerror("Erro", "Selecione um cargo para excluir!")
                return
            
            item = tree_cargos.item(selecionado[0])
            cargo_id = item['values'][0]
            cargo_nome = item['values'][1]
            
            resposta = messagebox.askyesno("Confirmar", f"Deseja excluir o cargo '{cargo_nome}'?\n\nObs: Funcionários já cadastrados com este cargo não serão afetados.")
            if resposta:
                try:
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    cursor.execute("DELETE FROM cargos WHERE id = ?", (cargo_id,))
                    conn.commit()
                    conn.close()
                    
                    messagebox.showinfo("Sucesso", "Cargo excluído com sucesso!")
                    carregar_cargos()
                    
                except Exception as e:
                    messagebox.showerror("Erro", f"Erro ao excluir cargo: {e}")
        
        # Botões
        tk.Button(frame_botoes, text="Adicionar Cargo", command=adicionar_cargo,
                 bg="#4CAF50", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
        
        tk.Button(frame_botoes, text="Excluir Cargo", command=excluir_cargo,
                 bg="#f44336", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
        
        tk.Button(frame_botoes, text="Fechar", command=janela_cargos.destroy,
                 bg="#2196F3", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
        
        # Carregar cargos ao abrir a janela
        carregar_cargos()
    
    def obter_cargos(self):
        """Retorna lista de cargos cadastrados"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT nome, salario_base FROM cargos ORDER BY nome")
            cargos = cursor.fetchall()
            conn.close()
            return cargos
        except Exception as e:
            print(f"Erro ao obter cargos: {e}")
            return []
    
    def obter_salario_cargo(self, nome_cargo):
        """Retorna o salário base de um cargo específico"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT salario_base FROM cargos WHERE nome = ?", (nome_cargo,))
            resultado = cursor.fetchone()
            conn.close()
            return resultado[0] if resultado else None
        except Exception as e:
            print(f"Erro ao obter salário do cargo: {e}")
            return None
    
    def executar(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = FolhaPagamento()
    app.executar()